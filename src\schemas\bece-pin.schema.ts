import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type BecePinDocument = BecePin & Document;

@Schema({ timestamps: true })
export class BecePin {
  @Prop({ required: true })
  pin: string; // Encrypted PIN

  @Prop({ required: true, unique: true })
  serialNumber: string;

  @Prop({ 
    required: true, 
    enum: ['AVAILABLE', 'PURCHASED', 'USED'], 
    default: 'AVAILABLE',
    index: true 
  })
  status: string;

  @Prop({ required: true })
  price: number;

  @Prop({ default: null })
  purchasedBy: string; // MSISDN of purchaser

  @Prop({ default: null })
  purchasedAt: Date;

  @Prop({ default: null })
  transactionId: string; // Reference to WaecTransaction

  @Prop({ default: null })
  email: string; // Email if delivery method is email

  @Prop({ default: null })
  network: string; // Network of purchaser

  @Prop({ default: false })
  isDelivered: boolean; // Whether PIN has been delivered via SMS/Email

  @Prop({ default: null })
  deliveredAt: Date;

  @Prop({ default: 'SMS', enum: ['SMS', 'EMAIL'] })
  deliveryMethod: string;
}

export const BecePinSchema = SchemaFactory.createForClass(BecePin);

// Create indexes for efficient queries
BecePinSchema.index({ status: 1 });
BecePinSchema.index({ serialNumber: 1 });
BecePinSchema.index({ purchasedBy: 1 });
BecePinSchema.index({ transactionId: 1 });
BecePinSchema.index({ status: 1, createdAt: 1 }); // For getting available pins in order
