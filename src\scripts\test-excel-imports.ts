/**
 * Simple test to verify Excel processing imports work correctly
 */

import * as XLSX from 'xlsx';

console.log('Testing Excel processing imports...');

// Test XLSX import
console.log('✅ XLSX imported successfully');
console.log('XLSX version:', XLSX.version);

// Test csv-parser import
console.log('✅ csv-parser imported successfully');

// Test service imports
console.log('✅ ExcelStreamProcessorService imported successfully');
console.log('✅ ExcelStreamReaderService imported successfully');

// Create a simple test Excel file
function createTestExcelFile(): string {
  const workbook = XLSX.utils.book_new();
  const data = [
    ['Name', 'Age', 'Email'],
    ['<PERSON>', 30, '<EMAIL>'],
    ['<PERSON>', 25, '<EMAIL>'],
    ['<PERSON>', 35, '<EMAIL>'],
  ];

  const worksheet = XLSX.utils.aoa_to_sheet(data);
  XLSX.utils.book_append_sheet(workbook, worksheet, 'TestData');

  const filePath = './test-excel-file.xlsx';
  XLSX.writeFile(workbook, filePath);

  console.log('✅ Test Excel file created:', filePath);
  return filePath;
}

// Test basic XLSX functionality
try {
  const testFile = createTestExcelFile();
  
  // Read the file back
  const workbook = XLSX.readFile(testFile);
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];
  const jsonData = XLSX.utils.sheet_to_json(worksheet);
  
  console.log('✅ Excel file read successfully');
  console.log('Data rows:', jsonData.length);
  console.log('Sample data:', jsonData[0]);

  // Clean up
  const fs = require('fs');
  if (fs.existsSync(testFile)) {
    fs.unlinkSync(testFile);
    console.log('✅ Test file cleaned up');
  }

  console.log('\n🎉 All imports and basic functionality working correctly!');
} catch (error) {
  console.error('❌ Error testing Excel functionality:', error.message);
  process.exit(1);
}
