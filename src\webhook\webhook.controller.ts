import { Body, Controller, HttpCode, HttpStatus, Post } from '@nestjs/common';
import { IncomingPaymentCompletionDto } from './dto/incoming-payment-completion.dto';
import { WebhookService } from './webhook.service';

@Controller('webhook')
export class WebhookController {
  constructor(private readonly webhookService: WebhookService) {}
  @Post()
  @HttpCode(HttpStatus.OK)
  async webhook(@Body() body: IncomingPaymentCompletionDto): Promise<void> {
    await this.webhookService.incomingRequest(body);
  }
}
