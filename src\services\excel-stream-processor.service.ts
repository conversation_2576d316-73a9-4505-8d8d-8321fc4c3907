import { Injectable, Logger } from '@nestjs/common';
import { Transform, Readable, pipeline } from 'stream';
import { promisify } from 'util';
import * as XLSX from 'xlsx';
import * as fs from 'fs';
import * as path from 'path';
// Alternative streaming library for better memory efficiency
// import * as XlsxStreamReader from 'xlsx-stream-reader';

const pipelineAsync = promisify(pipeline);

export interface ExcelProcessingOptions {
  sheetName?: string;
  sheetIndex?: number;
  batchSize?: number;
  skipEmptyRows?: boolean;
  headerRow?: number;
  maxRows?: number;
  onProgress?: (processed: number, total?: number) => void;
  onError?: (error: Error, rowIndex: number) => void;
}

export interface ProcessedRowResult {
  success: boolean;
  data?: any;
  error?: string;
  rowIndex: number;
}

@Injectable()
export class ExcelStreamProcessorService {
  private readonly logger = new Logger(ExcelStreamProcessorService.name);

  /**
   * Process Excel file using streaming with memory efficiency
   * @param filePath Path to the Excel file
   * @param processor Function to process each row
   * @param options Processing options
   */
  async processExcelFileStream<T>(
    filePath: string,
    processor: (row: any, rowIndex: number) => Promise<T> | T,
    options: ExcelProcessingOptions = {},
  ): Promise<{
    totalProcessed: number;
    successCount: number;
    errorCount: number;
    errors: Array<{ rowIndex: number; error: string }>;
  }> {
    const {
      sheetName,
      sheetIndex = 0,
      batchSize = 100,
      skipEmptyRows = true,
      headerRow = 0,
      maxRows,
      onProgress,
      onError,
    } = options;

    this.logger.log(`Starting Excel file processing: ${filePath}`);

    // Validate file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }

    const stats = {
      totalProcessed: 0,
      successCount: 0,
      errorCount: 0,
      errors: [] as Array<{ rowIndex: number; error: string }>,
    };

    try {
      // Read workbook with minimal memory usage
      const workbook = XLSX.readFile(filePath, {
        cellDates: true,
        cellNF: false,
        cellText: false,
        sheetStubs: false,
        bookDeps: false,
        bookFiles: false,
        bookProps: false,
        bookSheets: false,
        bookVBA: false,
      });

      // Get the target worksheet
      const worksheetName = sheetName || workbook.SheetNames[sheetIndex];
      const worksheet = workbook.Sheets[worksheetName];

      if (!worksheet) {
        throw new Error(`Worksheet not found: ${worksheetName}`);
      }

      // Get worksheet range for progress tracking
      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1');
      const totalRows = range.e.r - range.s.r + 1;
      const effectiveMaxRows = maxRows ? Math.min(maxRows, totalRows) : totalRows;

      this.logger.log(`Processing ${effectiveMaxRows} rows from sheet: ${worksheetName}`);

      // Create streaming transform
      const processingStream = this.createProcessingStream(
        processor,
        stats,
        headerRow,
        skipEmptyRows,
        onProgress,
        onError,
        effectiveMaxRows,
      );

      // Create Excel data stream
      const excelStream = this.createExcelDataStream(worksheet, range, batchSize);

      // Process the stream
      await pipelineAsync(excelStream, processingStream);

      this.logger.log(
        `Excel processing completed. Processed: ${stats.totalProcessed}, Success: ${stats.successCount}, Errors: ${stats.errorCount}`,
      );

      return stats;
    } catch (error) {
      this.logger.error(`Excel processing failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Create a readable stream from Excel worksheet data
   */
  private createExcelDataStream(
    worksheet: XLSX.WorkSheet,
    range: XLSX.Range,
    batchSize: number,
  ): Readable {
    let currentRow = range.s.r;
    const endRow = range.e.r;
    const self = this;

    return new Readable({
      objectMode: true,
      read() {
        try {
          if (currentRow > endRow) {
            this.push(null); // End of stream
            return;
          }

          // Process rows in batches to control memory usage
          const batchEnd = Math.min(currentRow + batchSize - 1, endRow);
          const batch = [];

          for (let r = currentRow; r <= batchEnd; r++) {
            const rowData: any = {};
            let hasData = false;

            // Read row data cell by cell
            for (let c = range.s.c; c <= range.e.c; c++) {
              const cellAddress = XLSX.utils.encode_cell({ r, c });
              const cell = worksheet[cellAddress];

              if (cell) {
                const columnName = XLSX.utils.encode_col(c);
                rowData[columnName] = self.getCellValue(cell);
                hasData = true;
              }
            }

            if (hasData) {
              batch.push({ rowIndex: r, data: rowData });
            }
          }

          currentRow = batchEnd + 1;

          // Push batch to stream
          for (const item of batch) {
            this.push(item);
          }
        } catch (error) {
          this.emit('error', error);
        }
      },
    });
  }

  /**
   * Create processing transform stream
   */
  private createProcessingStream<T>(
    processor: (row: any, rowIndex: number) => Promise<T> | T,
    stats: any,
    headerRow: number,
    skipEmptyRows: boolean,
    onProgress?: (processed: number, total?: number) => void,
    onError?: (error: Error, rowIndex: number) => void,
    totalRows?: number,
  ): Transform {
    let headerData: any = null;
    let processedCount = 0;
    const self = this;

    return new Transform({
      objectMode: true,
      async transform(chunk: { rowIndex: number; data: any }, _encoding, callback) {
        try {
          const { rowIndex, data } = chunk;

          // Handle header row
          if (rowIndex === headerRow) {
            headerData = data;
            callback();
            return;
          }

          // Skip empty rows if requested
          if (skipEmptyRows && self.isEmptyRow(data)) {
            callback();
            return;
          }

          // Convert to object with header keys if header exists
          let processedRow = data;
          if (headerData) {
            processedRow = self.mapRowToHeaders(data, headerData);
          }

          // Process the row
          try {
            const result = await processor(processedRow, rowIndex);
            stats.successCount++;

            // Emit processed data if needed
            this.push({ success: true, data: result, rowIndex });
          } catch (processingError) {
            stats.errorCount++;
            const errorMsg = processingError.message || 'Unknown processing error';
            stats.errors.push({ rowIndex, error: errorMsg });

            if (onError) {
              onError(processingError, rowIndex);
            }

            this.push({ success: false, error: errorMsg, rowIndex });
          }

          stats.totalProcessed++;
          processedCount++;

          // Report progress
          if (onProgress && processedCount % 100 === 0) {
            onProgress(processedCount, totalRows);
          }

          callback();
        } catch (error) {
          callback(error);
        }
      },
    });
  }

  /**
   * Get cell value with proper type conversion
   */
  private getCellValue(cell: XLSX.CellObject): any {
    if (!cell) return null;

    switch (cell.t) {
      case 'n': // Number
        return cell.v;
      case 's': // String
        return cell.v;
      case 'b': // Boolean
        return cell.v;
      case 'd': // Date
        return cell.v;
      case 'e': // Error
        return null;
      default:
        return cell.v;
    }
  }

  /**
   * Check if row is empty
   */
  private isEmptyRow(data: any): boolean {
    return !data || Object.values(data).every(value =>
      value === null || value === undefined || value === ''
    );
  }

  /**
   * Map row data to header keys
   */
  private mapRowToHeaders(data: any, headerData: any): any {
    const mapped: any = {};
    const headerKeys = Object.keys(headerData);
    const dataKeys = Object.keys(data);

    for (let i = 0; i < Math.max(headerKeys.length, dataKeys.length); i++) {
      const headerKey = headerKeys[i];
      const dataKey = dataKeys[i];

      if (headerKey && dataKey && headerData[headerKey]) {
        mapped[headerData[headerKey]] = data[dataKey];
      }
    }

    return mapped;
  }

  /**
   * Get Excel file information without loading entire file
   */
  async getExcelFileInfo(filePath: string): Promise<{
    sheetNames: string[];
    totalSheets: number;
    fileSize: number;
  }> {
    const stats = fs.statSync(filePath);
    const workbook = XLSX.readFile(filePath, {
      bookSheets: true,
      bookProps: false,
      sheetStubs: false,
    });

    return {
      sheetNames: workbook.SheetNames,
      totalSheets: workbook.SheetNames.length,
      fileSize: stats.size,
    };
  }
}
