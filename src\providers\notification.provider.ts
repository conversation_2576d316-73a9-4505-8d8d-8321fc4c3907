import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { WaecPin, WaecPinDocument } from '../schemas/waec-pin.schema';
import {
  WaecTransaction,
  WaecTransactionDocument,
} from '../schemas/waec-transaction.schema';
import { BecePin, BecePinDocument } from '../schemas/bece-pin.schema';
import { Logs, LogsDocument } from '../schemas/logs.schema';
import { catchError, firstValueFrom } from 'rxjs';
import { logger } from '../utils/logging';
import SendGrid from '@sendgrid/mail';
import { BecePinManagementService } from '../services/bece-pin-management.service';

@Injectable()
export class NotificationProvider {
  private readonly logger = new Logger(NotificationProvider.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    @InjectModel(WaecPin.name)
    private readonly waecPinModel: Model<WaecPinDocument>,
    @InjectModel(WaecTransaction.name)
    private readonly waecTransactionModel: Model<WaecTransactionDocument>,
    @InjectModel(BecePin.name)
    private readonly becePinModel: Model<BecePinDocument>,
    @InjectModel(Logs.name)
    private readonly logsModel: Model<LogsDocument>,
    private readonly becePinManagementService: BecePinManagementService,
  ) {
    // Initialize SendGrid
    SendGrid.setApiKey(this.configService.get<string>('SENDGRID_API_KEY'));
  }

  /**
   * Send notification based on the delivery method
   * @param transactionId The transaction ID
   */
  async sendNotification(transactionId: string): Promise<boolean> {
    try {
      // Find the transaction
      const transaction = await this.waecTransactionModel.findOne({
        transactionId,
        paymentStatus: 'COMPLETED',
        pinsGenerated: true,
        pinsDelivered: false,
      });

      if (!transaction) {
        this.logger.warn(
          `Transaction ${transactionId} not found or not ready for notification`,
        );
        return false;
      }

      // Find all pins associated with this transaction
      let pinDetails: Array<{ pin: string; serialNumber: string; examType: string }> = [];

      if (transaction.examType === 'BECE') {
        // For B.E.C.E, get pins from BecePin collection and decrypt them
        const becePins = await this.becePinModel.find({
          transactionId,
          isDelivered: false,
          status: 'PURCHASED',
        });

        if (!becePins || becePins.length === 0) {
          this.logger.warn(`No B.E.C.E pins found for transaction ${transactionId}`);
          return false;
        }

        // Use the BecePinManagementService to decrypt pins
        try {
          const decryptedPins = await this.becePinManagementService.allocatePinsForTransaction(
            transactionId,
            becePins.length,
          );
          pinDetails = decryptedPins;
        } catch (error) {
          this.logger.error(
            `Error decrypting B.E.C.E pins for transaction ${transactionId}: ${error.message}`,
          );
          return false;
        }
      } else {
        // For other exam types, use the existing WaecPin collection
        const pins = await this.waecPinModel.find({
          transactionId,
          isDelivered: false,
        });

        if (!pins || pins.length === 0) {
          this.logger.warn(`No pins found for transaction ${transactionId}`);
          return false;
        }

        // Format pins for notification
        pinDetails = pins.map((pin) => ({
          pin: pin.pin,
          serialNumber: pin.serialNumber,
          examType: pin.examType,
        }));
      }

      // Send notification based on delivery method
      let success = false;
      if (transaction.deliveryMethod === 'EMAIL' && transaction.email) {
        success = await this.sendEmailNotification(
          transaction.email,
          transaction.msisdn,
          pinDetails,
          transaction.examType,
        );
      } else {
        // Default to SMS
        success = await this.sendSmsNotification(
          transaction.msisdn,
          pinDetails,
          transaction.examType,
        );
      }

      // Update pin delivery status if successful
      if (success) {
        if (transaction.examType === 'BECE') {
          // Update B.E.C.E pins delivery status
          await Promise.all([
            this.becePinModel.updateMany(
              { transactionId },
              { isDelivered: true, deliveredAt: new Date() },
            ),
            this.waecTransactionModel.updateOne(
              { transactionId },
              { pinsDelivered: true },
            ),
          ]);
        } else {
          // Update regular WAEC pins delivery status
          await Promise.all([
            this.waecPinModel.updateMany(
              { transactionId },
              { isDelivered: true },
            ),
            this.waecTransactionModel.updateOne(
              { transactionId },
              { pinsDelivered: true },
            ),
          ]);
        }
      }

      return success;
    } catch (error) {
      this.logger.error(
        `Error sending notification for transaction ${transactionId}: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Send email notification with voucher details
   * @param email Recipient email
   * @param msisdn Phone number
   * @param pins Array of pin details
   * @param examType Type of exam
   */
  private async sendEmailNotification(
    email: string,
    msisdn: string,
    pins: Array<{ pin: string; serialNumber: string; examType: string }>,
    examType: string,
  ): Promise<boolean> {
    try {
      logger.info(
        `${NotificationProvider.name} ::: Sending email notification to ${email} for ${examType}`,
      );

      // Create email content
      const pinsHtml = pins
        .map(
          (pin) =>
            `<tr>
              <td style="padding: 8px; border: 1px solid #ddd;">${pin.serialNumber}</td>
              <td style="padding: 8px; border: 1px solid #ddd;">${pin.pin}</td>
            </tr>`,
        )
        .join('');

      const htmlContent = `
        <html>
          <body style="font-family: Arial, sans-serif; line-height: 1.6;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 10px;">
              <h2 style="color: #333;">Your WAEC Results Checker Vouchers</h2>
              <p>Hello,</p>
              <p>Thank you for your purchase. Below are your ${examType} results checker voucher(s):</p>
              
              <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
                <thead>
                  <tr style="background-color: #f2f2f2;">
                    <th style="padding: 12px; text-align: left; border: 1px solid #ddd;">Serial Number</th>
                    <th style="padding: 12px; text-align: left; border: 1px solid #ddd;">PIN</th>
                  </tr>
                </thead>
                <tbody>
                  ${pinsHtml}
                </tbody>
              </table>
              
              <p style="margin-top: 20px;">
                You can use these vouchers to check your ${examType} results on the official WAEC website.
              </p>
              
              <p style="color: #777; font-size: 14px; margin-top: 30px;">
                This is an automated message. Please do not reply to this email.
              </p>
            </div>
          </body>
        </html>
      `;

      // Send email using SendGrid
      const msg = {
        to: email,
        from: this.configService.get<string>(
          'EMAIL_FROM',
          '<EMAIL>',
        ),
        subject: `Your ${examType} Results Checker Vouchers`,
        text: `Your ${examType} Results Checker Vouchers\n\n${pins.map((pin) => `Serial Number: ${pin.serialNumber}\nPIN: ${pin.pin}\n`).join('\n')}`,
        html: htmlContent,
      };

      await SendGrid.send(msg);

      // Log email sent
      await this.logsModel.create({
        event: 'EMAIL_NOTIFICATION',
        msisdn,
        reference: pins[0]?.serialNumber,
        request: { email, count: pins.length, examType },
        response: { status: 'SENT' },
      });

      logger.info(
        `${NotificationProvider.name} ::: Email notification sent successfully to ${email}`,
      );
      return true;
    } catch (error) {
      this.logger.error(
        `Error sending email notification: ${error.message}`,
        error.stack,
      );

      // Log failure
      await this.logsModel.create({
        event: 'EMAIL_NOTIFICATION',
        msisdn,
        reference: pins[0]?.serialNumber,
        request: { email, count: pins.length, examType },
        response: { status: 'FAILED', error: error.message },
      });

      return false;
    }
  }

  /**
   * Send SMS notification with voucher details
   * @param msisdn Phone number
   * @param pins Array of pin details
   * @param examType Type of exam
   */
  private async sendSmsNotification(
    msisdn: string,
    pins: Array<{ pin: string; serialNumber: string; examType: string }>,
    examType: string,
  ): Promise<boolean> {
    try {
      logger.info(
        `${NotificationProvider.name} ::: Sending SMS notification to ${msisdn} for ${examType}`,
      );

      // Format the SMS message
      // Limit message length to fit SMS (consider multiple messages for many pins)
      const smsBody =
        pins.length === 1
          ? `Your ${examType} voucher: Serial: ${pins[0].serialNumber}, PIN: ${pins[0].pin}`
          : `Your ${pins.length} ${examType} vouchers: ${pins
            .slice(0, 3)
            .map((p) => `S/N: ${p.serialNumber} PIN: ${p.pin}`)
            .join('; ')}${pins.length > 3 ? '...' : ''}`;

      // Call SMS API using HttpService
      const smsApiUrl = this.configService.get<string>('SMS_API_URL');
      const smsApiKey = this.configService.get<string>('SMS_API_KEY');

      if (!smsApiUrl || !smsApiKey) {
        throw new Error('SMS API configuration is missing');
      }

      const payload = {
        recipient: msisdn,
        sender: this.configService.get<string>('SMS_SENDER', 'KAIROS'),
        message: smsBody,
        apiKey: smsApiKey,
      };

      const { data } = await firstValueFrom(
        this.httpService.post(smsApiUrl, payload).pipe(
          catchError((error) => {
            throw new Error(`SMS API request failed: ${error.message}`);
          }),
        ),
      );

      // Log SMS sent
      await this.logsModel.create({
        event: 'SMS_NOTIFICATION',
        msisdn,
        reference: pins[0]?.serialNumber,
        request: payload,
        response: data,
      });

      logger.info(
        `${NotificationProvider.name} ::: SMS notification sent successfully to ${msisdn}`,
      );
      return true;
    } catch (error) {
      this.logger.error(
        `Error sending SMS notification: ${error.message}`,
        error.stack,
      );

      // Log failure
      await this.logsModel.create({
        event: 'SMS_NOTIFICATION',
        msisdn,
        reference: pins[0]?.serialNumber,
        request: { msisdn, count: pins.length, examType },
        response: { status: 'FAILED', error: error.message },
      });

      return false;
    }
  }
}
