import { Test, TestingModule } from '@nestjs/testing';
import { UssdController } from './ussd.controller';
import { UssdService } from './ussd.service';
import {
  InboundUssdRequestResponseMockData,
  MOCK_SESSION_ID,
} from './__mocks__/ussd.mock';
import { MENU_OPTIONS, MENU_TITLE } from '../constants/constants';
import { fromInboundUssdRequestToResponsePayload } from './dto/inbound-request.dto';

describe('UssdController', () => {
  let controller: UssdController;
  let ussdService: UssdService;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UssdController],
      providers: [
        {
          provide: UssdService,
          useValue: {
            processInboundRequest: jest.fn(),
          },
        },
      ],
    }).compile();
    ussdService = module.get(UssdService);
    controller = module.get<UssdController>(UssdController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('#UssdMenuMenu', () => {
    it('should validate and return the main ussd menu items', async () => {
      jest
        .spyOn(ussdService, 'processInboundRequest')
        .mockReturnValue(
          fromInboundUssdRequestToResponsePayload(
            InboundUssdRequestResponseMockData,
            `${MENU_TITLE}${MENU_OPTIONS}`,
            '0',
          ) as any,
        );

      const response = await controller.inboundRequest(
        InboundUssdRequestResponseMockData,
      );
      expect(response.ussdString).toEqual(`${MENU_TITLE}${MENU_OPTIONS}`);
      expect(response).toHaveProperty('sessionId', MOCK_SESSION_ID);
      expect(ussdService.processInboundRequest).toHaveBeenCalledTimes(1);
    });
  });
});
