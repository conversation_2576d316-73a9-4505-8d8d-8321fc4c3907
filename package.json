{"name": "waec-results-checker-api", "private": true, "version": "1.0.0", "description": "API microservice for handling checking of waec results - NOVDEC, BECE, WASSCE", "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/jest/bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "generate-pins": "ts-node src/scripts/mock-generate-pins.ts", "test-notification": "ts-node src/scripts/test-notification.ts", "upload-bece-pins": "ts-node src/scripts/upload-bece-pins.ts", "upload-bece-pins-excel": "ts-node src/scripts/upload-bece-pins-excel-only.ts", "create-sample": "ts-node src/scripts/create-sample-excel.ts", "process-excel": "ts-node src/examples/excel-processing-examples.ts", "test-excel-streaming": "ts-node src/scripts/test-excel-streaming.ts", "test-excel-imports": "ts-node src/scripts/test-excel-imports.ts"}, "dependencies": {"@nestjs/axios": "^3.0.2", "@nestjs/cache-manager": "^2.2.2", "@nestjs/common": "^10.3.8", "@nestjs/config": "^3.2.2", "@nestjs/core": "^10.3.8", "@nestjs/mongoose": "^10.0.6", "@nestjs/platform-express": "^10.3.8", "@nestjs/schedule": "^4.0.2", "@sendgrid/mail": "^8.1.5", "@tirke/node-cache-manager-ioredis": "^3.6.0", "axios": "^1.7.2", "body-parser": "^1.20.1", "cache-manager": "^5.5.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.8.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "crypto-js": "^4.2.0", "csrf": "^3.1.0", "csv-parser": "^3.2.0", "express-xml-bodyparser": "^0.3.0", "fast-xml-parser": "^4.4.0", "helmet": "^6.0.1", "http-status-codes": "^2.3.0", "ioredis": "^5.4.1", "lodash": "^4.17.21", "moment": "^2.30.1", "mongoose": "^8.4.0", "reflect-metadata": "^0.2.1", "request-ip": "^3.3.0", "rxjs": "^7.8.1", "slugify": "^1.6.6", "ulid": "^2.3.0", "winston": "^3.13.0", "xlsx": "^0.18.5", "xlsx-parse-stream": "^1.1.0"}, "devDependencies": {"@nestjs/cli": "^10.3.1", "@nestjs/schematics": "^10.1.0", "@nestjs/testing": "^10.3.2", "@swc/cli": "^0.3.9", "@swc/core": "^1.4.0", "@types/cookie-parser": "^1.4.7", "@types/crypto-js": "^4.2.2", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/multer": "^2.0.0", "@types/node": "^20.11.16", "@types/request-ip": "^0.0.41", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "prettier": "^3.2.5", "source-map-support": "^0.5.21", "@types/compression": "^1.8.1", "supertest": "^6.3.4", "ts-jest": "^29.1.2", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}