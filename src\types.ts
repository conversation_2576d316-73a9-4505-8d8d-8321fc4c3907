import { generateRandomNumber } from './utils/helpers';
import { AxiosError } from 'axios';

export enum OfferRecipientEnums {
  SELF = 'SELF',
  OTHERS = 'OTHERS',
}

export interface IAccountHolderDetails {
  accountName: string;
  msisdn: string;
  network: string;
}

export interface IAPIResponse<T = any> {
  statusCode: string;
  statusMessage: string;
  transactionId: string;
  sequenceNo: string;
  data: T;
}

/**
 * Interfaces for oval data
 */
interface IOvalDataResponseData {
  transaction_id: string;
  payment_id: string;
  reference: string;
  status: string;
}

export interface IOvalDataResponse {
  status: string;
  code: string;
  message: string;
  data: IOvalDataResponseData;
}

export interface IPaymentDetails {
  totalAmount: number;
  amount: number;
  charge: number;
  referenceId: string;
}

export interface IPaymentRequestData {
  amount: number;
  referenceNo: string;
  payer: string;
  payee: string;
  narration: string;
  msisdn: string;
  callbackId: string;
}

export interface IPaymentSubscriptionSuccessResponse {
  body: IPaymentRequestData;
  data: any;
}

export interface IPaymentSubscriptionErrorResponse {
  data: any;
  err: AxiosError<unknown, any>;
}
