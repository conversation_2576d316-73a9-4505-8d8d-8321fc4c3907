/**
 * Test script for Excel streaming functionality
 * This script demonstrates memory-efficient processing of large Excel files
 */

import { MongooseModule } from '@nestjs/mongoose';
import { NestFactory } from '@nestjs/core';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ExcelStreamProcessorService } from '../services/excel-stream-processor.service';
import { ExcelStreamReaderService } from '../services/excel-stream-reader.service';
import { BecePinManagementService } from '../services/bece-pin-management.service';
import { BecePin, BecePinSchema } from '../schemas/bece-pin.schema';
import {
  WaecTransaction,
  WaecTransactionSchema,
} from '../schemas/waec-transaction.schema';
import * as XLSX from 'xlsx';
import * as fs from 'fs';
import * as path from 'path';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>('MONGODB_URI'),
      }),
      inject: [ConfigService],
    }),
    MongooseModule.forFeature([
      { name: BecePin.name, schema: BecePinSchema },
      { name: WaecTransaction.name, schema: WaecTransactionSchema },
    ]),
  ],
  providers: [
    ExcelStreamProcessorService,
    ExcelStreamReaderService,
    BecePinManagementService,
  ],
})
class ExcelStreamingTestModule {}

interface TestRecord {
  id: string;
  name: string;
  email: string;
  value: number;
}

class ExcelStreamingTester {
  constructor(
    private readonly streamProcessor: ExcelStreamProcessorService,
    private readonly streamReader: ExcelStreamReaderService,
    private readonly pinService: BecePinManagementService,
  ) {}

  /**
   * Generate a test Excel file with specified number of rows
   */
  generateTestFile(fileName: string, rowCount: number): string {
    console.log(`Generating test file with ${rowCount} rows...`);

    const workbook = XLSX.utils.book_new();
    const data = [];

    // Add header row
    data.push(['ID', 'Name', 'Email', 'Value', 'Date']);

    // Generate test data
    for (let i = 1; i <= rowCount; i++) {
      data.push([
        `ID${String(i).padStart(6, '0')}`,
        `Test User ${i}`,
        `user${i}@example.com`,
        Math.floor(Math.random() * 1000),
        new Date().toISOString(),
      ]);
    }

    const worksheet = XLSX.utils.aoa_to_sheet(data);
    XLSX.utils.book_append_sheet(workbook, worksheet, 'TestData');

    const filePath = path.join(process.cwd(), fileName);
    XLSX.writeFile(workbook, filePath);

    console.log(`Test file generated: ${filePath}`);
    return filePath;
  }

  /**
   * Generate B.E.C.E pins test file
   */
  generateBecePinsFile(fileName: string, pinCount: number): string {
    console.log(`Generating B.E.C.E pins file with ${pinCount} pins...`);

    const workbook = XLSX.utils.book_new();
    const data = [];

    // Add header row
    data.push(['PIN', 'Serial Number']);

    // Generate test pins
    for (let i = 1; i <= pinCount; i++) {
      const pin = Math.floor(1000000000 + Math.random() * 9000000000).toString();
      const serialNumber = `BECE${String(i).padStart(6, '0')}`;
      data.push([pin, serialNumber]);
    }

    const worksheet = XLSX.utils.aoa_to_sheet(data);
    XLSX.utils.book_append_sheet(workbook, worksheet, 'BECEPins');

    const filePath = path.join(process.cwd(), fileName);
    XLSX.writeFile(workbook, filePath);

    console.log(`B.E.C.E pins file generated: ${filePath}`);
    return filePath;
  }

  /**
   * Test basic streaming functionality
   */
  async testBasicStreaming(filePath: string): Promise<void> {
    console.log('\n=== Testing Basic Streaming ===');

    const startTime = Date.now();
    let processedCount = 0;

    const result = await this.streamProcessor.processExcelFileStream<TestRecord>(
      filePath,
      async (row: any, rowIndex: number) => {
        processedCount++;

        const record: TestRecord = {
          id: row['ID'] || row.A,
          name: row['Name'] || row.B,
          email: row['Email'] || row.C,
          value: parseFloat(row['Value'] || row.D) || 0,
        };

        // Simulate processing delay
        await new Promise(resolve => setTimeout(resolve, 1));

        return record;
      },
      {
        headerRow: 0,
        skipEmptyRows: true,
        batchSize: 100,
        onProgress: (processed) => {
          if (processed % 1000 === 0) {
            console.log(`Progress: ${processed} rows processed`);
          }
        },
        onError: (error, rowIndex) => {
          console.error(`Error at row ${rowIndex}: ${error.message}`);
        },
      },
    );

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`Basic streaming completed in ${duration}ms`);
    console.log(`Processed: ${result.totalProcessed}, Success: ${result.successCount}, Errors: ${result.errorCount}`);
    console.log(`Rows per second: ${Math.round(result.totalProcessed / (duration / 1000))}`);
  }

  /**
   * Test Transform stream processing (your requested pattern)
   */
  async testTransformStreaming(filePath: string): Promise<void> {
    console.log('\n=== Testing Transform Streaming ===');

    const startTime = Date.now();

    const result = await this.streamReader.processExcelWithTransform<TestRecord>(
      filePath,
      async (row: any, rowIndex: number) => {
        const record: TestRecord = {
          id: row['ID'] || row.A,
          name: row['Name'] || row.B,
          email: row['Email'] || row.C,
          value: parseFloat(row['Value'] || row.D) || 0,
        };

        // Simulate async processing
        await new Promise(resolve => setTimeout(resolve, 1));

        return record;
      },
      {
        headerRow: 0,
        skipEmptyRows: true,
        batchSize: 500,
        onProgress: (processed) => {
          if (processed % 2000 === 0) {
            const memUsage = process.memoryUsage();
            console.log(`Progress: ${processed} rows, Memory: ${Math.round(memUsage.heapUsed / 1024 / 1024)} MB`);
          }
        },
      },
    );

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`Transform streaming completed in ${duration}ms`);
    console.log(`Processed: ${result.totalProcessed}, Success: ${result.successCount}, Errors: ${result.errorCount}`);
    console.log(`Rows per second: ${Math.round(result.totalProcessed / (duration / 1000))}`);
  }

  /**
   * Test row-by-row processing for maximum memory efficiency
   */
  async testRowByRowProcessing(filePath: string): Promise<void> {
    console.log('\n=== Testing Row-by-Row Processing ===');

    const startTime = Date.now();

    const result = await this.streamReader.processExcelRowByRow<TestRecord>(
      filePath,
      async (row: any, rowIndex: number) => {
        const record: TestRecord = {
          id: row['ID'] || row.A,
          name: row['Name'] || row.B,
          email: row['Email'] || row.C,
          value: parseFloat(row['Value'] || row.D) || 0,
        };

        // Force garbage collection every 5000 rows
        if (rowIndex % 5000 === 0 && global.gc) {
          global.gc();
        }

        return record;
      },
      {
        headerRow: 0,
        skipEmptyRows: true,
        maxRows: 50000, // Limit for testing
        onProgress: (processed) => {
          if (processed % 5000 === 0) {
            const memUsage = process.memoryUsage();
            console.log(`Progress: ${processed} rows, Memory: ${Math.round(memUsage.heapUsed / 1024 / 1024)} MB`);
          }
        },
      },
    );

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`Row-by-row processing completed in ${duration}ms`);
    console.log(`Processed: ${result.totalProcessed}, Success: ${result.successCount}, Errors: ${result.errorCount}`);
    console.log(`Rows per second: ${Math.round(result.totalProcessed / (duration / 1000))}`);
  }

  /**
   * Test B.E.C.E pins processing
   */
  async testBecePinsProcessing(filePath: string): Promise<void> {
    console.log('\n=== Testing B.E.C.E Pins Processing ===');

    const pinsToUpload: Array<{ pin: string; serialNumber: string }> = [];
    const batchSize = 100;
    let uploadedTotal = 0;

    const result = await this.streamReader.processExcelWithTransform(
      filePath,
      async (row: any, rowIndex: number) => {
        const pinRecord = {
          pin: row['PIN'] || row.A,
          serialNumber: row['Serial Number'] || row.B,
        };

        // Validate PIN format
        if (!/^\d{10}$/.test(pinRecord.pin)) {
          throw new Error(`Invalid PIN format: ${pinRecord.pin}`);
        }

        pinsToUpload.push(pinRecord);

        // Upload in batches
        if (pinsToUpload.length >= batchSize) {
          try {
            const uploaded = await this.pinService.bulkUploadPins([...pinsToUpload], 12.0);
            uploadedTotal += uploaded;
            console.log(`Uploaded batch: ${uploaded} pins (Total: ${uploadedTotal})`);
          } catch (error) {
            console.error(`Batch upload error: ${error.message}`);
          }
          pinsToUpload.length = 0; // Clear array
        }

        return pinRecord;
      },
      {
        headerRow: 0,
        skipEmptyRows: true,
        onProgress: (processed) => {
          if (processed % 500 === 0) {
            console.log(`Processed ${processed} pins`);
          }
        },
        onError: (error, rowIndex) => {
          console.error(`Pin processing error at row ${rowIndex}: ${error.message}`);
        },
      },
    );

    // Upload remaining pins
    if (pinsToUpload.length > 0) {
      try {
        const uploaded = await this.pinService.bulkUploadPins(pinsToUpload, 12.0);
        uploadedTotal += uploaded;
        console.log(`Uploaded final batch: ${uploaded} pins (Total: ${uploadedTotal})`);
      } catch (error) {
        console.error(`Final batch upload error: ${error.message}`);
      }
    }

    console.log(`B.E.C.E pins processing completed`);
    console.log(`Processed: ${result.totalProcessed}, Success: ${result.successCount}, Errors: ${result.errorCount}`);
    console.log(`Total pins uploaded: ${uploadedTotal}`);
  }

  /**
   * Memory usage monitoring
   */
  startMemoryMonitoring(): NodeJS.Timeout {
    return setInterval(() => {
      const memUsage = process.memoryUsage();
      console.log(`[MEMORY] Heap Used: ${Math.round(memUsage.heapUsed / 1024 / 1024)} MB, Heap Total: ${Math.round(memUsage.heapTotal / 1024 / 1024)} MB`);
    }, 10000);
  }

  /**
   * Clean up test files
   */
  cleanupTestFiles(filePaths: string[]): void {
    filePaths.forEach(filePath => {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log(`Cleaned up: ${filePath}`);
      }
    });
  }
}

async function runTests() {
  const app = await NestFactory.createApplicationContext(ExcelStreamingTestModule);
  
  const streamProcessor = app.get(ExcelStreamProcessorService);
  const streamReader = app.get(ExcelStreamReaderService);
  const pinService = app.get(BecePinManagementService);
  
  const tester = new ExcelStreamingTester(streamProcessor, streamReader, pinService);

  try {
    console.log('Starting Excel Streaming Tests...');
    
    // Start memory monitoring
    const memoryMonitor = tester.startMemoryMonitoring();

    // Generate test files
    const testFile = tester.generateTestFile('test-data.xlsx', 10000);
    const pinsFile = tester.generateBecePinsFile('test-pins.xlsx', 1000);

    // Run tests
    await tester.testBasicStreaming(testFile);
    await tester.testTransformStreaming(testFile);
    await tester.testRowByRowProcessing(testFile);
    await tester.testBecePinsProcessing(pinsFile);

    // Stop memory monitoring
    clearInterval(memoryMonitor);

    // Cleanup
    tester.cleanupTestFiles([testFile, pinsFile]);

    console.log('\nAll tests completed successfully!');
  } catch (error) {
    console.error('Test failed:', error.message);
    console.error(error.stack);
  } finally {
    await app.close();
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}
