import { Modu<PERSON> } from '@nestjs/common';
import { WebhookController } from './webhook.controller';
import { WebhookService } from './webhook.service';
import { MongooseModule } from '@nestjs/mongoose';
import { Purchases, PurchaseSchema } from '../schemas/purchases.schema';
import { Logs, LogsSchema } from '../schemas/logs.schema';
import { HttpModule } from '@nestjs/axios';
import { Transaction, TransactionSchema } from '../schemas/transaction.schema';
import { NotificationProvider } from '../providers/notification.provider';
import { WaecPin, WaecPinSchema } from '../schemas/waec-pin.schema';
import {
  WaecTransaction,
  WaecTransactionSchema,
} from '../schemas/waec-transaction.schema';

import { BecePinManagementService } from '../services/bece-pin-management.service';

@Module({
  imports: [
    HttpModule,
    MongooseModule.forFeature([
      { name: Purchases.name, schema: PurchaseSchema },
      { name: Logs.name, schema: LogsSchema },
      { name: Transaction.name, schema: TransactionSchema },
      { name: WaecTransaction.name, schema: WaecTransactionSchema },
      { name: WaecPin.name, schema: WaecPinSchema },
    ]),
  ],
  controllers: [WebhookController],
  providers: [WebhookService, NotificationProvider, BecePinManagementService],
})
export class WebhookModule { }
