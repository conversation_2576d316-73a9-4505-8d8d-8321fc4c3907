import { createHmac } from 'crypto';
import { AES } from 'crypto-js';
import { InboundRequestDto } from '../ussd/dto/inbound-request.dto';
import { logger } from './logging';

export const encrypt = (value: string, secret: string) => {
  return AES.encrypt(value, secret);
};

export const apiPrefix = (env: string) => {
  return ['production', 'staging'].includes(env) ? '/v1' : '/api/v1';
};

export const generateRandomNumber = (): string => {
  return `${Math.floor(100 + Math.random() * 900)}${Math.floor(10000 + Math.random() * 90000)}`;
};

export const generateCode = (prefix: string) => {
  return `${prefix}${Math.floor(1000000000 + Math.random() * 9000000000)}`?.trim();
};

export const isMsisdnWhitelisted = (msisdn: string, customerId: string) => {
  return customerId?.trim()?.split(',').includes(msisdn);
};

export const generateSignature = (value: any, secret: string) => {
  return createHmac('sha256', secret).update(value).digest('hex');
};

export const buildApiResponse = <T>(
  statusCode: string,
  statusMessage: string,
  transactionId: string,
  data: T,
  link?: string,
) => {
  return {
    statusCode,
    statusMessage,
    transactionId,
    data,
    ...(link && {
      link,
    }),
  };
};

export const base64Encode = (text: string): string => {
  const bytes = Buffer.from(text);
  return bytes.toString('base64');
};

export const base64Decode = (text: string) => {
  return Buffer.from(text, 'base64').toString('utf-8');
};

export const defaultIfEmpty = <K, V>(key: K, value: V) => {
  if (typeof key === 'object') {
    return Object.keys(key).length > 0 ? key : value;
  }
  return key === undefined || key === null ? value : key;
};

export const formalizeServiceCode = (code: string) => {
  return code?.trim()?.replace(/\W+/g, '');
};

export const chunk = <T>(
  records: Array<T>,
  chunkSize: number,
): Array<Array<T>> => {
  return new Array(Math.ceil(records.length / chunkSize))
    .fill(0)
    .map((item, index) => {
      return index > 0
        ? records?.slice(
            index > 0 ? chunkSize * index : chunkSize,
            chunkSize * (index + 1),
          )
        : records?.slice(index, chunkSize);
    });
};

export const buildPagination = <T = any>(
  docs: T[],
  totalDocs: number,
  page: number,
  size: number,
) => {
  return {
    docs,
    totalDocs,
    totalPages: Math.ceil(totalDocs / size),
    page,
    size,
  };
};

export const validateServiceCode = (
  { ussdString }: InboundRequestDto,
  serviceCode: string,
) => {
  const userInput = ussdString?.trim()?.split('#').shift();
  return (
    userInput === '' ||
    userInput === `${serviceCode}` ||
    userInput === `*${serviceCode}#` ||
    userInput === `*${serviceCode}`
  );
};

export const parseJson = (key: any) => {
  if (typeof key === 'string') {
    return JSON.parse(key);
  }
  return key;
};

/**
 * Generate a random string of specified length
 * @param length Length of the string to generate
 * @param alphanumeric If true, include only letters and numbers
 * @returns Random string
 */
export const generateRandomString = (
  length: number,
  alphanumeric = false,
): string => {
  const characters = alphanumeric
    ? 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    : 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'; // Removed similar-looking characters like O/0, I/1

  let result = '';
  const charactersLength = characters.length;

  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }

  return result;
};
