import { Inject, Injectable } from '@nestjs/common';
import {
  activeSessionRef,
  buildUssdSessionActivity,
  UssdStagesDto,
} from '../ussd/dto/ussd-stages.dto';
import {
  fromInboundUssdRequestToResponsePayload,
  InboundRequestDto,
} from '../ussd/dto/inbound-request.dto';
import { logger } from '../utils/logging';
import {
  ACTIVE_TTL,
  APP_NAME,
  BACK_00,
  FINAL_STEP,
  NETWORKS,
  NEXT_99,
  OFFER_TITLE,
  RECIPIENTS,
  USSD_DATA,
  USSD_SESSION,
} from '../constants/constants';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { RedisCache } from '@tirke/node-cache-manager-ioredis';
import { UssdDataDto } from '../ussd/dto/ussd-data.dto';
import { QueryOffersProvider } from './query-offers.provider';
import { OffersDocument } from '../schemas/offers.schema';
import { OfferRecipientEnums } from '../types';
import { UssdFlowCompletionProvider } from './ussd-flow-completion.provider';

@Injectable()
export class UssdStageInputProvider {
  constructor(
    @Inject(CACHE_MANAGER) private readonly cacheRepository: RedisCache,
    private readonly queryOfferProvider: QueryOffersProvider,
    private readonly ussdFlowCompletionProvider: UssdFlowCompletionProvider,
  ) {}

  async processStageInputRequest(
    cachedInboundUssdStages: Array<UssdStagesDto>,
    inboundRequest: InboundRequestDto,
  ): Promise<any> {
    logger.info(
      '%s ::: Incoming request payload for stage input ::: Cached Input ::: %s',
      UssdStageInputProvider.name,
      JSON.stringify(inboundRequest),
    );
    const previousUssdSessionStage =
      cachedInboundUssdStages[cachedInboundUssdStages.length - 1];
    if (![1, 2].includes(Number(inboundRequest.ussdString))) {
      return fromInboundUssdRequestToResponsePayload(
        inboundRequest,
        previousUssdSessionStage.ussdString,
        '0',
      );
    }
    const { hashMap } = await this.queryOfferProvider.findAll(
      NETWORKS[Number(inboundRequest.ussdString) - 1],
    );
    const ussdString = `${OFFER_TITLE}\n${(hashMap.get('1') as string[])?.join('\n')}\n${hashMap.size > 1 ? `${BACK_00}${NEXT_99}` : ''}`;
    await this.cacheRepository.set(
      activeSessionRef(inboundRequest.msisdn, USSD_DATA),
      {
        menuItem: NETWORKS[Number(inboundRequest.ussdString) - 1],
      },
      ACTIVE_TTL,
    );
    const ussdStagesDtos: Array<UssdStagesDto> = buildUssdSessionActivity<
      any,
      UssdStagesDto
    >(cachedInboundUssdStages, {
      ussdString,
      previousStep: 1,
      currentStep: 2,
      totalPages: hashMap.size,
      page: 1,
    });
    await this.cacheRepository.set(
      activeSessionRef(inboundRequest.msisdn, USSD_SESSION),
      ussdStagesDtos,
      ACTIVE_TTL,
    );

    return fromInboundUssdRequestToResponsePayload(
      inboundRequest,
      ussdString,
      '0',
    );
  }

  async processInputFlowService(
    cachedInboundUssdStages: Array<UssdStagesDto>,
    inboundRequest: InboundRequestDto,
  ): Promise<any> {
    const previousCachedUssdStage =
      cachedInboundUssdStages[cachedInboundUssdStages.length - 1];
    const cachedUssdData = await this.cacheRepository.get<
      UssdDataDto<OffersDocument>
    >(activeSessionRef(inboundRequest.msisdn, USSD_DATA));
    logger.info(
      '%s :: Cached Ussd Session Previous Record ::: %s',
      UssdStageInputProvider.name,
      JSON.stringify(previousCachedUssdStage),
    );

    if (!previousCachedUssdStage.finalStep) {
      switch (previousCachedUssdStage.currentStep) {
        case 2: {
          logger.info(
            '%s ::: Choosing an offer from the list of provided menu items ::: [Offer Input <> %s] ',
            UssdStageInputProvider.name,
            inboundRequest.ussdString,
          );
          return await this.handleProductOfferingSelection(
            cachedInboundUssdStages,
            inboundRequest,
            cachedUssdData,
          );
        }
        case 3: {
          logger.info(
            '%s ::: Option for buying for [self/others] <> User Input :: %s',
            UssdStageInputProvider.name,
            inboundRequest.ussdString,
          );
          if (![1, 2].includes(Number(inboundRequest.ussdString))) {
            return fromInboundUssdRequestToResponsePayload(
              inboundRequest,
              previousCachedUssdStage.ussdString,
              '0',
            );
          }

          const summaryContent = `Purchase Summary\nPackage: ${cachedUssdData?.offer?.volume}${cachedUssdData?.offer?.unitOfMeasure}\nPrice: GHC${cachedUssdData?.offer?.price}\n${FINAL_STEP}`;
          const offerRecipient =
            RECIPIENTS[Number(inboundRequest.ussdString) - 1];

          const ussdString =
            offerRecipient === OfferRecipientEnums.SELF
              ? summaryContent
              : `Enter recipient's mobile number\n`;

          await this.cacheRepository.set(
            activeSessionRef(inboundRequest.msisdn, USSD_DATA),
            {
              ...cachedUssdData,
              recipientType: offerRecipient,
              msisdn: inboundRequest.msisdn,
              summaryContent,
            },
            ACTIVE_TTL,
          );

          await this.cacheRepository.set(
            activeSessionRef(inboundRequest.msisdn, USSD_SESSION),
            buildUssdSessionActivity(cachedInboundUssdStages, {
              ...previousCachedUssdStage,
              previousStep: 3,
              currentStep: 4,
              ussdString,
              finalStep: offerRecipient === OfferRecipientEnums.SELF,
            }),
            ACTIVE_TTL,
          );
          return fromInboundUssdRequestToResponsePayload(
            inboundRequest,
            ussdString,
            '0',
          );
        }
        // skip this step when buying for self
        case 4: {
          if (inboundRequest.ussdString === '') {
            return fromInboundUssdRequestToResponsePayload(
              inboundRequest,
              previousCachedUssdStage.ussdString,
              '0',
            );
          }
          const ussdString = `Confirm recipient's mobile number\n`;
          await this.cacheRepository.set(
            activeSessionRef(inboundRequest.msisdn, USSD_SESSION),
            buildUssdSessionActivity(cachedInboundUssdStages, {
              ...previousCachedUssdStage,
              previousStep: 4,
              currentStep: 5,
              ussdString,
            }),
            ACTIVE_TTL,
          );
          await this.cacheRepository.set(
            activeSessionRef(inboundRequest.msisdn, USSD_DATA),
            {
              ...cachedUssdData,
              recipientPhoneNumber: inboundRequest.ussdString,
            },
            ACTIVE_TTL,
          );
          return fromInboundUssdRequestToResponsePayload(
            inboundRequest,
            ussdString,
            '0',
          );
        }
        case 5: {
          if (inboundRequest.ussdString === '') {
            return fromInboundUssdRequestToResponsePayload(
              inboundRequest,
              previousCachedUssdStage.ussdString,
              '0',
            );
          }
          if (
            cachedUssdData.recipientPhoneNumber !== inboundRequest.ussdString
          ) {
            return fromInboundUssdRequestToResponsePayload(
              inboundRequest,
              `Mobile number doesn't match\n${previousCachedUssdStage.ussdString}`,
              '0',
            );
          }
          await this.cacheRepository.set(
            activeSessionRef(inboundRequest.msisdn, USSD_SESSION),
            buildUssdSessionActivity(cachedInboundUssdStages, {
              ...previousCachedUssdStage,
              previousStep: 5,
              ussdString: cachedUssdData.summaryContent,
              currentStep: 6,
              finalStep: true,
            }),
            ACTIVE_TTL,
          );
          return fromInboundUssdRequestToResponsePayload(
            inboundRequest,
            cachedUssdData.summaryContent,
            '0',
          );
        }
      }
    } else {
      return await this.ussdFlowCompletionProvider.incomingUssdFlowCompletion(
        inboundRequest,
      );
    }
  }

  private handleProductOfferingSelection = async (
    cachedInboundUssdStages: Array<UssdStagesDto>,
    inboundRequest: InboundRequestDto,
    cachedUssdData: UssdDataDto,
  ): Promise<InboundRequestDto> => {
    logger.info(
      '%s :::: Managing customer input selection :: %s',
      UssdStageInputProvider.name,
      inboundRequest.ussdString,
    );
    const previousCachedUssdStage =
      cachedInboundUssdStages[cachedInboundUssdStages.length - 1];
    const slicedUssdStages = cachedInboundUssdStages.slice(
      0,
      cachedInboundUssdStages.length - 1,
    );

    logger.info(
      '%s ::: Ussd Cached Data ::: %s',
      UssdStageInputProvider.name,
      JSON.stringify(cachedUssdData),
    );
    switch (inboundRequest.ussdString) {
      case '99': {
        if (
          !previousCachedUssdStage.totalPages ||
          previousCachedUssdStage.totalPages <= 1
        ) {
          return fromInboundUssdRequestToResponsePayload(
            inboundRequest,
            previousCachedUssdStage.ussdString,
            '0',
          );
        }
        const nextPage = previousCachedUssdStage.page + 1;
        const pageData: string[] =
          await this.queryOfferProvider.getCachedPagedOffers(
            `${nextPage >= previousCachedUssdStage.totalPages ? nextPage : 1}`,
            cachedUssdData?.menuItem,
          );
        logger.info(
          '%s ::: Page ::: %s ::: Data ::: %s',
          UssdStageInputProvider.name,
          nextPage,
          pageData,
        );
        await this.cacheRepository.set(
          activeSessionRef(inboundRequest.msisdn, USSD_SESSION),
          buildUssdSessionActivity(slicedUssdStages, {
            ...previousCachedUssdStage,
            page: nextPage,
          }),
          ACTIVE_TTL,
        );
        const ussdString = `${OFFER_TITLE}\n${pageData?.join('\n')}\n${nextPage >= previousCachedUssdStage.totalPages ? BACK_00 : `${NEXT_99}${BACK_00}`}`;
        return fromInboundUssdRequestToResponsePayload(
          inboundRequest,
          ussdString,
          '0',
        );
      }
      case '00': {
        if (previousCachedUssdStage.page === 1) {
          await this.cacheRepository.set(
            activeSessionRef(inboundRequest.msisdn, USSD_SESSION),
            slicedUssdStages,
            ACTIVE_TTL,
          );
          const ussdStage = slicedUssdStages[slicedUssdStages.length - 1];
          return fromInboundUssdRequestToResponsePayload(
            inboundRequest,
            ussdStage.ussdString,
            '0',
          );
        }
        const previousPage = previousCachedUssdStage.page - 1;
        const pageData: string[] =
          await this.queryOfferProvider.getCachedPagedOffers(
            `${previousPage <= previousCachedUssdStage.totalPages ? previousPage : 1}`,
            cachedUssdData?.menuItem,
          );
        await this.cacheRepository.set(
          activeSessionRef(inboundRequest.msisdn, USSD_SESSION),
          buildUssdSessionActivity(slicedUssdStages, {
            ...previousCachedUssdStage,
            page: previousPage < 0 ? 1 : previousPage,
          }),
          ACTIVE_TTL,
        );
        const ussdString = `${OFFER_TITLE}\n${pageData?.join('\n')}\n${`${NEXT_99}${BACK_00}`}`;
        return fromInboundUssdRequestToResponsePayload(
          inboundRequest,
          ussdString,
          '0',
        );
      }
      default: {
        logger.info(
          '%s ::: User selected package ::: %s',
          UssdStageInputProvider.name,
          inboundRequest.ussdString,
        );
        const activeOffers = (await this.cacheRepository.get(
          `${APP_NAME}:offers:${cachedUssdData.menuItem}`,
        )) as Array<OffersDocument>;

        if (
          inboundRequest.ussdString === '' ||
          isNaN(Number(inboundRequest.ussdString)) ||
          (Number(inboundRequest.ussdString) <= 0 &&
            Number(inboundRequest.ussdString) > activeOffers.length)
        ) {
          return fromInboundUssdRequestToResponsePayload(
            inboundRequest,
            previousCachedUssdStage.ussdString,
            '0',
          );
        }
        const ussdString = `Buy for\n1.Self\n2.Others`;
        const activeOffer = activeOffers.find(
          (_, index) => index + 1 === Number(inboundRequest.ussdString),
        );
        logger.info(
          '%s ::: Selected Offer ::: %s',
          UssdStageInputProvider.name,
          JSON.stringify(activeOffer),
        );
        await this.cacheRepository.set(
          activeSessionRef(inboundRequest.msisdn, USSD_SESSION),
          buildUssdSessionActivity(cachedInboundUssdStages, {
            ...previousCachedUssdStage,
            previousStep: 2,
            currentStep: 3,
            ussdString,
          }),
          ACTIVE_TTL,
        );
        await this.cacheRepository.set(
          activeSessionRef(inboundRequest.msisdn, USSD_DATA),
          {
            ...cachedUssdData,
            offer: activeOffer,
          },
          ACTIVE_TTL,
        );
        return fromInboundUssdRequestToResponsePayload(
          inboundRequest,
          ussdString,
          '0',
        );
      }
    }
  };
}
