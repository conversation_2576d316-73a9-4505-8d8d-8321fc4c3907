import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type WaecTransactionDocument = WaecTransaction & Document;

@Schema({ timestamps: true })
export class WaecTransaction {
  @Prop({ required: true, unique: true })
  transactionId: string;

  @Prop({ required: true })
  msisdn: string;

  @Prop({ required: true, enum: ['WASSCE', 'BECE', 'NOVDEC', 'ABCE/GBCE'] })
  examType: string;

  @Prop({ required: true })
  quantity: number;

  @Prop({ required: true })
  unitPrice: number;

  @Prop({ required: true })
  totalAmount: number;

  @Prop()
  paymentReference: string;

  @Prop({ enum: ['PENDING', 'COMPLETED', 'FAILED'], default: 'PENDING' })
  paymentStatus: string;

  @Prop()
  network: string;

  @Prop()
  email: string;

  @Prop({ enum: ['SMS', 'EMAIL'] })
  deliveryMethod: string;

  @Prop({ default: false })
  pinsGenerated: boolean;

  @Prop({ default: false })
  pinsDelivered: boolean;
}

export const WaecTransactionSchema =
  SchemaFactory.createForClass(WaecTransaction);
