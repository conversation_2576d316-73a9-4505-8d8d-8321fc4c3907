export const APP_NAME = 'waec-results-checker-ussd-api';
export const ACTIVE_TTL = 60 * 5;
export const MENU_TITLE = `Buy Results Checker\n`;
export const NETWORKS = ['MTN', 'AirtelTigo', 'Vodafone'];
export const MENU_OPTIONS = `1.BECE\n2.WASSCE\n3.NOVDEC\n4.ABCE/GBCE`;
export const OFFER_TITLE = `Choose an offer.`;
export const BACK_00 = `00.Back`;
export const NEXT_99 = `99.Next`;
export const NOTIFICATION_CHANNELS = `\n1.Email\n2.SMS`;
export const NOTIFICATION_MENU_ITEMS = `Receive Vouchers As:${NOTIFICATION_CHANNELS}\n`;
export const ENTER_EMAIL = `Enter your email address.\n`;
export const ACTIVE_SESSIONS = `${APP_NAME}:session`;
export const ENTER_QUANTITY = `Enter quantity.\nEg.1`;
export const FINAL_STEP = `1.Confirm Payment\n2.Cancel`;
export const USSD_SESSION = 'stages';
export const USSD_DATA = 'data';
export const COMPLETED_FLOW_MESSAGE = `Payment is being processed. You should receive a prompt soon.`;
export const ITEMS_PER_PAGE = 3;
export const RECIPIENTS = ['SELF', 'OTHERS'];
export const EXAM_TYPES = {
  WASSCE: 'WASSCE',
  BECE: 'BECE',
  NOVDEC: 'NOVDEC',
  ABCE_GBCE: 'ABCE/GBCE',
};

export const EXAM_PRICES = {
  [EXAM_TYPES.WASSCE]: 15.0,
  [EXAM_TYPES.BECE]: 12.0,
  [EXAM_TYPES.NOVDEC]: 16.0,
  [EXAM_TYPES.ABCE_GBCE]: 14.0,
};
