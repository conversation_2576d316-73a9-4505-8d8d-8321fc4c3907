import { IsEnum, <PERSON>N<PERSON><PERSON>, IsOptional, IsString } from 'class-validator';
import { EXAM_TYPES } from '../../constants/constants';

export class WaecUssdDataDto {
  @IsOptional()
  @IsEnum(EXAM_TYPES)
  examType?: string;

  @IsOptional()
  @IsNumber()
  quantity?: number;

  @IsOptional()
  @IsNumber()
  totalAmount?: number;

  @IsOptional()
  @IsString()
  email?: string;

  @IsOptional()
  @IsString()
  deliveryMethod?: 'SMS' | 'EMAIL';

  @IsOptional()
  @IsString()
  transactionId?: string;
}
