name: Build and deploy image to ACR

on:
  pull_request:
    types:
      - closed
    branches:
      - staging
permissions:
  id-token: write
  contents: read

jobs:
  build-and-deploy:
    if: github.event.pull_request.merged == true
    environment: staging
    runs-on: ubuntu-latest

    steps:
      - name: Checkout github codes
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        id: docker-build
        uses: docker/setup-buildx-action@v2

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: |
            handel2023.azurecr.io/waec-ussd-api
          tags: type=sha,format=long

      - uses: azure/docker-login@v1
        with:
          login-server: handel2023.azurecr.io
          username: ${{ secrets.AZURE_REGISTRY_USERNAME }}
          password: ${{ secrets.AZURE_REGISTRY_PASSWORD }}

      - name: <PERSON><PERSON> and  <PERSON>ush
        uses: docker/build-push-action@v3
        with:
          context: .
          file: ./dockerfiles/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
