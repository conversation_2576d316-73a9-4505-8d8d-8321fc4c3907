import { ACTIVE_SESSIONS } from '../../constants/constants';

export class UssdStagesDto {
  ussdString: string | null;
  productOption?: string;
  subProductOption?: string | null;
  finalStep?: boolean;
  previousStep: number | null;
  currentStep: number | null;
  totalPages?: number = 2;
  page?: number = 1;
  size?: number = 3;
}

export const buildUssdSessionActivity = <T = any[], V = any>(
  cachedPayload: T[],
  incomingRequest: V,
) => {
  return [...cachedPayload, incomingRequest];
};

export const activeSessionRef = (sessionId: string, ussdState: string) =>
  `${ACTIVE_SESSIONS}:${sessionId}:${ussdState}`;
