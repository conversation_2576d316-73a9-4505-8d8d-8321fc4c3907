import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, SchemaTypes } from 'mongoose';
import { OffersDocument } from './offers.schema';

export type PurchasesDocument = HydratedDocument<Purchases>;

@Schema({
  timestamps: true,
})
export class Purchases {
  @Prop({
    type: Number,
    default: 0,
  })
  totalAmount: number;

  @Prop({
    type: Number,
    default: 0,
  })
  overallTotalAmount: number;

  @Prop({
    type: String,
    required: true,
  })
  senderMsisdn: string;

  @Prop({
    type: String,
    index: true,
  })
  recipientMsisdn: string;

  @Prop({
    type: SchemaTypes.Mixed,
    default: null,
  })
  offer: OffersDocument | null;

  @Prop({
    type: String,
    required: true,
    index: true,
  })
  paymentReference: string;

  @Prop({
    type: String,
    enum: ['STARTED', 'COMPLETED'],
    default: `STARTED`,
  })
  status: string;
}

export const PurchaseSchema = SchemaFactory.createForClass(Purchases);
