import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ConfigService } from '@nestjs/config';
import { WaecPin, WaecPinDocument } from '../schemas/waec-pin.schema';
import { WaecTransaction, WaecTransactionDocument } from '../schemas/waec-transaction.schema';
import { AES, enc } from 'crypto-js';
import { logger } from '../utils/logging';

export interface AllocatedPin {
  pin: string; // Decrypted PIN
  serialNumber: string;
  examType: string;
}

@Injectable()
export class BecePinManagementService {
  private readonly logger = new Logger(BecePinManagementService.name);

  constructor(
    @InjectModel(WaecPin.name)
    private readonly becePinModel: Model<WaecPinDocument>,
    @InjectModel(WaecTransaction.name)
    private readonly waecTransactionModel: Model<WaecTransactionDocument>,
    private readonly configService: ConfigService,
  ) { }

  /**
   * Allocate B.E.C.E pins for a successful payment
   * @param transactionId - The transaction ID from WaecTransaction
   * @param quantity - Number of pins to allocate
   * @returns Array of allocated pins with decrypted PINs
   */
  async allocatePinsForTransaction(
    transactionId: string,
    quantity: number,
  ): Promise<AllocatedPin[]> {
    logger.info(
      `${BecePinManagementService.name} ::: Allocating ${quantity} B.E.C.E pins for transaction ${transactionId}`,
    );

    // Get the transaction details
    const transaction = await this.waecTransactionModel.findOne({
      transactionId,
      examType: 'BECE',
      paymentStatus: 'COMPLETED',
    });

    if (!transaction) {
      throw new BadRequestException(
        `Transaction ${transactionId} not found or not completed for BECE`,
      );
    }

    // Check if pins have already been allocated for this transaction
    const existingPins = await this.becePinModel.find({
      transactionId,
      status: 'PURCHASED',
    });

    if (existingPins.length > 0) {
      logger.warn(
        `${BecePinManagementService.name} ::: Pins already allocated for transaction ${transactionId}`,
      );
      // Return the already allocated pins (decrypt them)
      return this.decryptPins(existingPins, 'BECE');
    }

    // Find available pins
    const availablePins = await this.becePinModel
      .find({ status: 'AVAILABLE' })
      .sort({ createdAt: 1 }) // First in, first out
      .limit(quantity)
      .exec();

    if (availablePins.length < quantity) {
      throw new BadRequestException(
        `Insufficient B.E.C.E pins available. Requested: ${quantity}, Available: ${availablePins.length}`,
      );
    }

    // Update the pins to PURCHASED status
    const pinIds = availablePins.map((pin) => pin._id);
    await this.becePinModel.updateMany(
      { _id: { $in: pinIds } },
      {
        status: 'PURCHASED',
        purchasedBy: transaction.msisdn,
        purchasedAt: new Date(),
        transactionId: transaction.transactionId,
        email: transaction.email,
        network: transaction.network,
        deliveryMethod: transaction.deliveryMethod,
      },
    );

    // Get the updated pins
    const allocatedPins = await this.becePinModel.find({
      _id: { $in: pinIds },
    });

    logger.info(
      `${BecePinManagementService.name} ::: Successfully allocated ${allocatedPins.length} B.E.C.E pins for transaction ${transactionId}`,
    );

    // Return decrypted pins for notification
    return this.decryptPins(allocatedPins, 'BECE');
  }

  /**
   * Decrypt pins for delivery
   * @param pins - Array of encrypted pins from database
   * @param examType - Exam type for the pins
   * @returns Array of decrypted pins
   */
  private decryptPins(pins: WaecPinDocument[], examType: string): AllocatedPin[] {
    const encryptionKey = this.configService.get<string>('PIN_ENCRYPTION_KEY');

    if (!encryptionKey) {
      throw new Error('PIN_ENCRYPTION_KEY not configured');
    }

    return pins.map((pin) => ({
      pin: this.decryptPin(pin.pin, encryptionKey),
      serialNumber: pin.serialNumber,
      examType,
    }));
  }

  /**
   * Decrypt a single PIN
   * @param encryptedPin - Encrypted PIN from database
   * @param key - Encryption key
   * @returns Decrypted PIN
   */
  private decryptPin(encryptedPin: string, key: string): string {
    try {
      const bytes = AES.decrypt(encryptedPin, key);
      return bytes.toString(enc.Utf8);
    } catch (error) {
      this.logger.error(
        `Error decrypting PIN: ${error.message}`,
        error.stack,
      );
      throw new Error('Failed to decrypt PIN');
    }
  }

  /**
   * Encrypt a PIN for storage
   * @param pin - Plain text PIN
   * @param key - Encryption key
   * @returns Encrypted PIN
   */
  private encryptPin(pin: string, key: string): string {
    return AES.encrypt(pin, key).toString();
  }

  /**
   * Mark pins as delivered
   * @param transactionId - Transaction ID
   */
  async markPinsAsDelivered(transactionId: string): Promise<void> {
    await this.becePinModel.updateMany(
      { transactionId, status: 'PURCHASED' },
      {
        isDelivered: true,
        deliveredAt: new Date(),
      },
    );

    logger.info(
      `${BecePinManagementService.name} ::: Marked pins as delivered for transaction ${transactionId}`,
    );
  }

  /**
   * Get available pin count
   * @returns Number of available B.E.C.E pins
   */
  async getAvailablePinCount(): Promise<number> {
    return this.becePinModel.countDocuments({ status: 'AVAILABLE' });
  }

  /**
   * Bulk upload B.E.C.E pins
   * @param pins - Array of pins to upload
   * @param price - Price per pin
   * @returns Number of pins uploaded
   */
  async bulkUploadPins(
    pins: Array<{ pin: string; serialNumber: string }>,
    price: number,
  ): Promise<number> {
    const encryptionKey = this.configService.get<string>('PIN_ENCRYPTION_KEY');

    if (!encryptionKey) {
      throw new Error('PIN_ENCRYPTION_KEY not configured');
    }

    const encryptedPins = pins.map((pinData) => ({
      pin: this.encryptPin(pinData.pin, encryptionKey),
      serialNumber: pinData.serialNumber,
      price,
      status: 'AVAILABLE',
      examType: 'BECE',
    }));

    // Use insertMany with ordered: false to continue on duplicate key errors
    try {
      const result = await this.becePinModel.insertMany(encryptedPins, {
        ordered: false,
      });

      logger.info(
        `${BecePinManagementService.name} ::: Successfully uploaded ${result.length} B.E.C.E pins`,
      );

      return result.length;
    } catch (error) {
      // Handle duplicate key errors gracefully
      if (error.code === 11000) {
        const insertedCount = error.result?.insertedCount || 0;
        logger.warn(
          `${BecePinManagementService.name} ::: Bulk upload completed with ${insertedCount} pins inserted. Some duplicates were skipped.`,
        );
        return insertedCount;
      }
      throw error;
    }
  }
}
