import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
  Inject,
} from '@nestjs/common';
import { HttpAdapterHost } from '@nestjs/core';
import { getReasonPhrase } from 'http-status-codes';
import { logger } from '../utils/logging';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { RedisCache } from '@tirke/node-cache-manager-ioredis';
import { generateRandomNumber } from '../utils/helpers';
export type IExpressResponse = Response & {
  message: string;
  error?: string;
};

@Catch()
export class AllExceptionsFilter<T> implements ExceptionFilter {
  constructor(
    private readonly httpAdapterHost: HttpAdapterHost,
    @Inject(CACHE_MANAGER) private readonly cacheRepository: RedisCache,
  ) {}

  catch(exception: T, host: ArgumentsHost) {
    try {
      const { httpAdapter } = this.httpAdapterHost;
      const ctx = host.switchToHttp();
      ctx.getRequest().body;
      const httpStatus =
        exception instanceof HttpException
          ? exception?.getStatus()
          : HttpStatus?.INTERNAL_SERVER_ERROR;
      const httpBody =
        exception instanceof HttpException
          ? exception?.getResponse()
          : exception;

      const responseBody = {
        statusCode: httpStatus,
        statusMessage: getReasonPhrase(httpStatus)?.toUpperCase(),
        supportMessage: this.buildErrorMessage(
          (httpBody as IExpressResponse)?.message ?? (httpBody as string),
        ),
        transactionId:
          ctx.getRequest().headers?.transactionid ?? generateRandomNumber(),
        timestamp: new Date().toISOString(),
        path: ctx.getRequest()?.url,
      };
      logger.error(responseBody);
      httpAdapter.reply(ctx?.getResponse(), responseBody, httpStatus);
    } catch (e) {}
  }

  buildErrorMessage(message: string | string[]): string {
    if (Array.isArray(message)) {
      return message?.shift();
    }
    return message;
  }
}
