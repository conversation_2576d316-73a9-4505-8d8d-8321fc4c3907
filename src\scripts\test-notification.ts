/**
 * This is a utility script to test the notification provider directly.
 */

import { MongooseModule } from '@nestjs/mongoose';
import { NestFactory } from '@nestjs/core';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { WaecPin, WaecPinSchema } from '../schemas/waec-pin.schema';
import {
  WaecTransaction,
  WaecTransactionSchema,
} from '../schemas/waec-transaction.schema';
import { Logs, LogsSchema } from '../schemas/logs.schema';
import { NotificationProvider } from '../providers/notification.provider';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    HttpModule,
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>('MONGODB_URI'),
      }),
      inject: [ConfigService],
    }),
    MongooseModule.forFeature([
      { name: WaecPin.name, schema: WaecPinSchema },
      { name: WaecTransaction.name, schema: WaecTransactionSchema },
      { name: Logs.name, schema: LogsSchema },
    ]),
  ],
  providers: [NotificationProvider],
})
class AppModule {}

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const notificationProvider = app.get(NotificationProvider);

  // Get transaction ID from command line arguments
  const transactionId = process.argv[2];

  if (!transactionId) {
    console.error('Please provide a transaction ID as a command line argument');
    process.exit(1);
  }

  try {
    console.log(`Testing notification for transaction: ${transactionId}`);
    const result = await notificationProvider.sendNotification(transactionId);
    console.log(`Notification result: ${result ? 'Success' : 'Failed'}`);
  } catch (error) {
    console.error('Error sending notification:', error);
  }

  await app.close();
}

bootstrap()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('Error in notification test script:', error);
    process.exit(1);
  });
