import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { UssdModule } from './ussd/ussd.module';
import { WebhookModule } from './webhook/webhook.module';
import { CacheModule } from '@nestjs/cache-manager';
import { ioRedisStore } from '@tirke/node-cache-manager-ioredis';
import { HttpModule } from '@nestjs/axios';
import { WaecPin, WaecPinSchema } from './schemas/waec-pin.schema';
import {
  WaecTransaction,
  WaecTransactionSchema,
} from './schemas/waec-transaction.schema';
import * as process from 'node:process';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    CacheModule.registerAsync({
      isGlobal: true,
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        store: await ioRedisStore({
          host: configService.get('REDIS_HOST', 'localhost'),
          port: configService.get('REDIS_PORT', 6379),
          username: configService.get('REDIS_USERNAME', ''),
          password: configService.get('REDIS_PASSWORD', ''),
          ttl: 60 * 5 * 1000, // 5 minutes
        }),
      }),
      inject: [ConfigService],
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>('MONGODB_URI'),
        retryAttempts: 5,
      }),
      inject: [ConfigService],
    }),
    MongooseModule.forFeature([
      { name: WaecPin.name, schema: WaecPinSchema },
      { name: WaecTransaction.name, schema: WaecTransactionSchema },
    ]),
    HttpModule,
    UssdModule,
    WebhookModule,
  ],
  providers: [],
  exports: [],
})
export class AppModule {}
