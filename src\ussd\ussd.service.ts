import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { RedisCache } from '@tirke/node-cache-manager-ioredis';
import { Model } from 'mongoose';
import { ulid } from 'ulid';
import {
  ACTIVE_TTL,
  ENTER_EMAIL,
  ENTER_QUANTITY,
  EXAM_PRICES,
  EXAM_TYPES,
  FINAL_STEP,
  MENU_OPTIONS,
  MENU_TITLE,
  NOTIFICATION_MENU_ITEMS,
  USSD_DATA,
  USSD_SESSION,
} from '../constants/constants';
import { UssdFlowCompletionProvider } from '../providers/ussd-flow-completion.provider';
import { UssdStageInputProvider } from '../providers/ussd-stage-input.provider';
import {
  WaecTransaction,
  WaecTransactionDocument,
} from '../schemas/waec-transaction.schema';
import { logger } from '../utils/logging';
import {
  fromInboundUssdRequestToResponsePayload,
  InboundRequestDto,
} from './dto/inbound-request.dto';
import {
  activeSessionRef,
  buildUssdSessionActivity,
  UssdStagesDto,
} from './dto/ussd-stages.dto';
import { WaecUssdDataDto } from './dto/waec-ussd-data.dto';

@Injectable()
export class UssdService {
  private readonly logger = new Logger(UssdService.name);

  constructor(
    private readonly configService: ConfigService,
    @Inject(CACHE_MANAGER) private readonly cacheRepository: RedisCache,
    private readonly ussdStageInputProvider: UssdStageInputProvider,
    @InjectModel(WaecTransaction.name)
    private waecTransactionModel: Model<WaecTransactionDocument>,
    private readonly ussdFlowCompletionProvider: UssdFlowCompletionProvider,
  ) {}

  async processInboundRequest(
    inboundRequest: InboundRequestDto,
  ): Promise<InboundRequestDto> {
    logger.info(`Incoming inbound request: %s`, JSON.stringify(inboundRequest));

    const { sessionId, ussdString, msisdn, network } = inboundRequest;

    try {
      // New session
      if (!ussdString) {
        await this.initializeSession(sessionId, msisdn);
        return fromInboundUssdRequestToResponsePayload(
          inboundRequest,
          `${MENU_TITLE}${MENU_OPTIONS}`,
          '0',
        );
      }

      // Get current session state
      const ussdStages = await this.getSessionStages(sessionId);
      const currentStage = ussdStages[ussdStages.length - 1]; // Get the most recent stage
      const ussdData = await this.getSessionData(sessionId);

      // Process based on current stage
      switch (currentStage.currentStep) {
        case 1: // Main menu - select exam type
          return this.handleExamTypeSelection(
            inboundRequest,
            ussdStages,
            ussdData,
          );
        case 2: // Select Delivery Method (SMS or Email)
          return this.handleDeliveryMethodSelection(
            inboundRequest,
            ussdStages,
            ussdData,
          );
        case 3: // Enter Email Address (only if email selected)
          return this.handleEmailInput(inboundRequest, ussdStages, ussdData);
        case 4: // Enter quantity
          return this.handleQuantityInput(inboundRequest, ussdStages, ussdData);
        case 5: // Confirmation
          return this.handlePaymentConfirmation(
            inboundRequest,
            ussdStages,
            ussdData,
          );
        default:
          return fromInboundUssdRequestToResponsePayload(
            inboundRequest,
            `Invalid session state. Please try again.\n`,
            '1',
          );
      }
    } catch (error) {
      this.logger.error(
        `Error processing USSD request: ${error.message}`,
        error.stack,
      );
      return fromInboundUssdRequestToResponsePayload(
        inboundRequest,
        `Sorry, an error occurred. Please try again later.\n`,
        '1',
      );
    }
  }

  // handle continue session here
  async handleContinueSession(
    cachedInboundUssdStages: Array<UssdStagesDto>,
    inboundRequest: InboundRequestDto,
  ): Promise<any> {
    if (
      !(await this.cacheRepository.get(
        activeSessionRef(inboundRequest.msisdn, USSD_SESSION),
      ))
    ) {
      return fromInboundUssdRequestToResponsePayload(
        inboundRequest,
        `Invalid Session State.Try again.\n`,
        '1',
      );
    }
    const ussdStagesDto =
      cachedInboundUssdStages[cachedInboundUssdStages.length - 1];
    if (
      ussdStagesDto.previousStep === null &&
      ussdStagesDto.currentStep === 1
    ) {
      return this.ussdStageInputProvider.processStageInputRequest(
        cachedInboundUssdStages,
        inboundRequest,
      );
    }
    if (ussdStagesDto.previousStep !== null && ussdStagesDto.currentStep > 1) {
      return this.ussdStageInputProvider.processInputFlowService(
        cachedInboundUssdStages,
        inboundRequest,
      );
    }
  }

  private async initializeSession(
    sessionId: string,
    msisdn: string,
  ): Promise<void> {
    // Initialize session stage as an array with the initial stage
    const initialStage = {
      ussdString: null,
      previousStep: null,
      currentStep: 1,
      finalStep: false,
    };

    const ussdStagesDtos: Array<UssdStagesDto> = buildUssdSessionActivity<
      any,
      UssdStagesDto
    >([], initialStage);

    // Initialize session data
    const data = new WaecUssdDataDto();

    // Store in cache
    await this.cacheRepository.set(
      activeSessionRef(sessionId, USSD_SESSION),
      ussdStagesDtos,
      ACTIVE_TTL,
    );
    await this.cacheRepository.set(
      activeSessionRef(sessionId, USSD_DATA),
      data,
      ACTIVE_TTL,
    );
  }

  private async getSessionStages(
    sessionId: string,
  ): Promise<Array<UssdStagesDto>> {
    const cachedStages = await this.cacheRepository.get<Array<UssdStagesDto>>(
      activeSessionRef(sessionId, USSD_SESSION),
    );

    if (!cachedStages || cachedStages.length === 0) {
      throw new NotFoundException('Session not found');
    }

    return cachedStages;
  }

  private async getSessionData(sessionId: string): Promise<WaecUssdDataDto> {
    const cachedData = await this.cacheRepository.get<WaecUssdDataDto>(
      activeSessionRef(sessionId, USSD_DATA),
    );

    if (!cachedData) {
      throw new NotFoundException('Session data not found');
    }

    return cachedData;
  }

  private async updateSessionStages(
    sessionId: string,
    newStage: UssdStagesDto,
    existingStages: Array<UssdStagesDto>,
  ): Promise<void> {
    // Add the new stage to the existing stages array
    const updatedStages = buildUssdSessionActivity<any, UssdStagesDto>(
      existingStages,
      newStage,
    );

    await this.cacheRepository.set(
      activeSessionRef(sessionId, USSD_SESSION),
      updatedStages,
      ACTIVE_TTL,
    );
  }

  private async updateSessionData(
    sessionId: string,
    data: WaecUssdDataDto,
  ): Promise<void> {
    await this.cacheRepository.set(
      activeSessionRef(sessionId, USSD_DATA),
      data,
      ACTIVE_TTL,
    );
  }

  private async handleExamTypeSelection(
    requestPayload: InboundRequestDto,
    stages: Array<UssdStagesDto>,
    data: WaecUssdDataDto,
  ): Promise<InboundRequestDto> {
    const { sessionId, ussdString } = requestPayload;
    const option = Number(ussdString);
    const currentStage = stages[stages.length - 1]; // Get current stage

    // Validate selection
    if (![1, 2, 3, 4].includes(option)) {
      return fromInboundUssdRequestToResponsePayload(
        requestPayload,
        `Invalid selection.\n${MENU_TITLE}${MENU_OPTIONS}`,
        '0',
      );
    }

    // Only BECE (option 1) is active, show inactive message for other options
    if (option !== 1) {
      return fromInboundUssdRequestToResponsePayload(
        requestPayload,
        `Sorry, this option is currently inactive. Only BECE vouchers are available.\n`,
        '1',
      );
    }

    // Map selection to exam type (only BECE is active)
    const examType = EXAM_TYPES.BECE;

    // Update session data
    data.examType = examType;
    await this.updateSessionData(sessionId, data);

    // Create new stage
    const newStage: UssdStagesDto = {
      ussdString,
      previousStep: currentStage.currentStep,
      currentStep: 2,
      finalStep: false,
    };

    // Update session stages
    await this.updateSessionStages(sessionId, newStage, stages);

    // Return response with notification options
    return fromInboundUssdRequestToResponsePayload(
      requestPayload,
      NOTIFICATION_MENU_ITEMS,
      '0',
    );
  }

  private async handleQuantityInput(
    requestPayload: InboundRequestDto,
    stages: Array<UssdStagesDto>,
    data: WaecUssdDataDto,
  ): Promise<InboundRequestDto> {
    const { sessionId, ussdString } = requestPayload;
    const quantity = Number(ussdString);
    const currentStage = stages[stages.length - 1]; // Get current stage

    // Validate quantity
    if (isNaN(quantity) || quantity <= 0 || quantity > 10) {
      return fromInboundUssdRequestToResponsePayload(
        requestPayload,
        `Invalid quantity. Please enter a number between 1 and 10.\n${ENTER_QUANTITY}`,
        '0',
      );
    }

    // Calculate total amount
    const unitPrice = EXAM_PRICES[data.examType];
    const totalAmount = unitPrice * quantity;

    // Update session data
    data.quantity = quantity;
    data.totalAmount = totalAmount;
    await this.updateSessionData(sessionId, data);

    // Create new stage
    const newStage: UssdStagesDto = {
      ussdString,
      previousStep: currentStage.currentStep, // Should be 4 (or 3 if skipped email)
      currentStep: 5, // Step 5: Payment confirmation
      finalStep: false,
    };

    // Update session stages
    await this.updateSessionStages(sessionId, newStage, stages);

    // Return response
    return fromInboundUssdRequestToResponsePayload(
      requestPayload,
      `Make payment of an amount of GHC ${totalAmount.toFixed(2)} for ${quantity} ${data.examType} results checkers.\n${FINAL_STEP}`,
      '0',
    );
  }

  private async handleDeliveryMethodSelection(
    requestPayload: InboundRequestDto,
    stages: Array<UssdStagesDto>,
    data: WaecUssdDataDto,
  ): Promise<InboundRequestDto> {
    const { sessionId, ussdString } = requestPayload;
    const option = Number(ussdString);
    const currentStage = stages[stages.length - 1]; // Get current stage

    // Validate selection
    if (![1, 2].includes(option)) {
      return fromInboundUssdRequestToResponsePayload(
        requestPayload,
        `Invalid selection.\n${NOTIFICATION_MENU_ITEMS}`,
        '0',
      );
    }

    // Update session data (SMS=2, EMAIL=1 as per NOTIFICATION_CHANNELS)
    const deliveryMethod = option === 1 ? 'EMAIL' : 'SMS';
    data.deliveryMethod = deliveryMethod;
    await this.updateSessionData(sessionId, data);

    let nextPrompt: string;
    let nextCurrentStep: number;

    if (deliveryMethod === 'EMAIL') {
      nextPrompt = ENTER_EMAIL;
      nextCurrentStep = 3; // Step 3: Enter Email
    } else {
      nextPrompt = ENTER_QUANTITY;
      nextCurrentStep = 4; // Step 4: Enter Quantity
    }

    // Create new stage
    const newStage: UssdStagesDto = {
      ussdString,
      previousStep: currentStage.currentStep,
      currentStep: nextCurrentStep,
      finalStep: false,
    };

    // Update session stages
    await this.updateSessionStages(sessionId, newStage, stages);

    // Return response for the next step
    return fromInboundUssdRequestToResponsePayload(
      requestPayload,
      nextPrompt,
      '0',
    );
  }

  private async handleEmailInput(
    requestPayload: InboundRequestDto,
    stages: Array<UssdStagesDto>,
    data: WaecUssdDataDto,
  ): Promise<InboundRequestDto> {
    const { sessionId, ussdString } = requestPayload;
    const currentStage = stages[stages.length - 1]; // Get current stage

    // Basic email validation
    const email = ussdString.trim();
    if (!email || !email.includes('@') || email.length < 5) {
      return fromInboundUssdRequestToResponsePayload(
        requestPayload,
        `Invalid email.\n${ENTER_EMAIL}`,
        '0',
      );
    }

    // Store email in session data
    data.email = email;
    await this.updateSessionData(sessionId, data);

    // Create new stage for Quantity Input
    const newStage: UssdStagesDto = {
      ussdString: email,
      previousStep: currentStage.currentStep, // Should be 3
      currentStep: 4, // Step 4: Enter Quantity
      finalStep: false,
    };

    // Update session stages
    await this.updateSessionStages(sessionId, newStage, stages);

    // Return response asking for Quantity
    return fromInboundUssdRequestToResponsePayload(
      requestPayload,
      ENTER_QUANTITY,
      '0',
    );
  }

  private async handlePaymentConfirmation(
    requestPayload: InboundRequestDto,
    stages: Array<UssdStagesDto>,
    data: WaecUssdDataDto,
  ): Promise<InboundRequestDto> {
    const { sessionId, ussdString, msisdn, network } = requestPayload;
    const option = Number(ussdString);
    const currentStage = stages[stages.length - 1]; // Get current stage

    // Validate selection
    if (![1, 2].includes(option)) {
      return fromInboundUssdRequestToResponsePayload(
        requestPayload,
        `Invalid selection.\nMake payment of an amount of GHC ${data.totalAmount.toFixed(2)} for ${data.quantity} ${data.examType} results checkers.\n${FINAL_STEP}`,
        '0',
      );
    }

    // Cancel
    if (option === 2) {
      // Create final stage indicating cancellation
      const finalStage: UssdStagesDto = {
        ussdString,
        previousStep: currentStage.currentStep, // Should be 5
        currentStep: 6, // End stage (6)
        finalStep: true,
      };

      await this.updateSessionStages(sessionId, finalStage, stages);

      // Clear the Redis cache for the cancelled session
      await this.cacheRepository.del(activeSessionRef(sessionId, USSD_SESSION));
      await this.cacheRepository.del(activeSessionRef(sessionId, USSD_DATA));

      return fromInboundUssdRequestToResponsePayload(
        requestPayload,
        `Transaction cancelled. Thank you for using our service.\n`,
        '1',
      );
    }

    // At this point, option must be 1 (confirm payment)
    try {
      // Generate transaction ID
      const transactionId = ulid();
      data.transactionId = transactionId;

      // Save transaction with delivery method and email
      await this.createTransaction(
        data,
        msisdn,
        network,
        data.deliveryMethod as 'SMS' | 'EMAIL',
        data.email,
      );

      // Create final stage indicating completion
      const finalStage: UssdStagesDto = {
        ussdString,
        previousStep: currentStage.currentStep, // Should be 5
        currentStep: 6, // End stage (6)
        finalStep: true,
      };

      await this.updateSessionStages(sessionId, finalStage, stages);

      // Use the UssdFlowCompletionProvider to handle payment initiation
      return await this.ussdFlowCompletionProvider.incomingUssdFlowCompletion(
        requestPayload,
      );
    } catch (error) {
      this.logger.error(
        `Error processing payment: ${error.message}`,
        error.stack,
      );

      // Clear the Redis cache for the failed session
      await this.cacheRepository.del(activeSessionRef(sessionId, USSD_SESSION));
      await this.cacheRepository.del(activeSessionRef(sessionId, USSD_DATA));

      return fromInboundUssdRequestToResponsePayload(
        requestPayload,
        `Sorry, we encountered an error processing your payment. Please try again later.\n`,
        '1',
      );
    }
  }

  private async createTransaction(
    data: WaecUssdDataDto,
    msisdn: string,
    network: string,
    deliveryMethod: 'SMS' | 'EMAIL',
    email?: string,
  ): Promise<WaecTransactionDocument> {
    const transaction = new this.waecTransactionModel({
      transactionId: data.transactionId,
      examType: data.examType,
      quantity: data.quantity,
      unitPrice: EXAM_PRICES[data.examType],
      totalAmount: data.totalAmount,
      msisdn,
      network,
      paymentStatus: 'PENDING',
      pinsGenerated: false,
      pinsDelivered: false,
      deliveryMethod,
      email,
    });

    return transaction.save();
  }
}
