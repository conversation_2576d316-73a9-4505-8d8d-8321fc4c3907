/**
 * Examples demonstrating memory-efficient Excel file processing
 * These examples show how to handle large XLSX files (100MB+) without memory issues
 */

import { ExcelStreamProcessorService } from '../services/excel-stream-processor.service';
import { ExcelStreamReaderService } from '../services/excel-stream-reader.service';
import { BecePinManagementService } from '../services/bece-pin-management.service';
import { Logger } from '@nestjs/common';

const logger = new Logger('ExcelProcessingExamples');

// Example data interfaces
interface StudentRecord {
  studentId: string;
  name: string;
  email: string;
  grade: number;
  subject: string;
}

interface PinRecord {
  pin: string;
  serialNumber: string;
  examType: string;
}

/**
 * Example 1: Processing large student records file
 */
export async function processStudentRecords(
  filePath: string,
  excelService: ExcelStreamProcessorService,
): Promise<void> {
  logger.log('Starting student records processing...');

  try {
    const result = await excelService.processExcelFileStream<StudentRecord>(
      filePath,
      async (row: any, rowIndex: number) => {
        // Validate and transform row data
        const studentRecord: StudentRecord = {
          studentId: row['Student ID'] || row.A,
          name: row['Name'] || row.B,
          email: row['Email'] || row.C,
          grade: parseFloat(row['Grade'] || row.D) || 0,
          subject: row['Subject'] || row.E,
        };

        // Validate required fields
        if (!studentRecord.studentId || !studentRecord.name) {
          throw new Error(`Missing required fields in row ${rowIndex}`);
        }

        // Simulate database save or API call
        await saveStudentRecord(studentRecord);

        return studentRecord;
      },
      {
        headerRow: 0,
        skipEmptyRows: true,
        batchSize: 500,
        onProgress: (processed) => {
          if (processed % 1000 === 0) {
            logger.log(`Processed ${processed} student records`);
          }
        },
        onError: (error, rowIndex) => {
          logger.error(`Error processing row ${rowIndex}: ${error.message}`);
        },
      },
    );

    logger.log(`Student processing completed: ${JSON.stringify(result)}`);
  } catch (error) {
    logger.error(`Student processing failed: ${error.message}`);
    throw error;
  }
}

/**
 * Example 2: Processing B.E.C.E pins from Excel file
 */
export async function processBecePinsFromExcel(
  filePath: string,
  excelService: ExcelStreamReaderService,
  pinService: BecePinManagementService,
): Promise<void> {
  logger.log('Starting B.E.C.E pins processing from Excel...');

  const pinsToUpload: Array<{ pin: string; serialNumber: string }> = [];
  const batchSize = 1000;

  try {
    const result = await excelService.processExcelWithTransform<PinRecord>(
      filePath,
      async (row: any, rowIndex: number) => {
        // Extract pin data from row
        const pinRecord: PinRecord = {
          pin: row['PIN'] || row.A,
          serialNumber: row['Serial Number'] || row.B,
          examType: 'BECE',
        };

        // Validate pin data
        if (!pinRecord.pin || !pinRecord.serialNumber) {
          throw new Error(`Invalid pin data in row ${rowIndex}`);
        }

        // Validate PIN format (10 digits)
        if (!/^\d{10}$/.test(pinRecord.pin)) {
          throw new Error(`Invalid PIN format in row ${rowIndex}: ${pinRecord.pin}`);
        }

        // Add to batch
        pinsToUpload.push({
          pin: pinRecord.pin,
          serialNumber: pinRecord.serialNumber,
        });

        // Upload in batches to avoid memory buildup
        if (pinsToUpload.length >= batchSize) {
          await uploadPinBatch([...pinsToUpload], pinService);
          pinsToUpload.length = 0; // Clear array
        }

        return pinRecord;
      },
      {
        headerRow: 0,
        skipEmptyRows: true,
        maxRows: 100000, // Limit for safety
        onProgress: (processed) => {
          if (processed % 5000 === 0) {
            logger.log(`Processed ${processed} pins`);
          }
        },
        onError: (error, rowIndex) => {
          logger.error(`Error processing pin row ${rowIndex}: ${error.message}`);
        },
      },
    );

    // Upload remaining pins
    if (pinsToUpload.length > 0) {
      await uploadPinBatch(pinsToUpload, pinService);
    }

    logger.log(`B.E.C.E pins processing completed: ${JSON.stringify(result)}`);
  } catch (error) {
    logger.error(`B.E.C.E pins processing failed: ${error.message}`);
    throw error;
  }
}

/**
 * Example 3: Memory-efficient row-by-row processing for very large files
 */
export async function processVeryLargeFile(
  filePath: string,
  excelService: ExcelStreamReaderService,
): Promise<void> {
  logger.log('Starting very large file processing...');

  try {
    const result = await excelService.processExcelRowByRow(
      filePath,
      async (row: any, rowIndex: number) => {
        // Process each row with minimal memory usage
        const processedData = {
          id: row.A,
          value: row.B,
          timestamp: new Date(),
          rowIndex,
        };

        // Simulate processing (database insert, API call, etc.)
        await processRowData(processedData);

        // Force garbage collection every 10000 rows for very large files
        if (rowIndex % 10000 === 0 && global.gc) {
          global.gc();
        }

        return processedData;
      },
      {
        headerRow: 0,
        skipEmptyRows: true,
        onProgress: (processed) => {
          if (processed % 10000 === 0) {
            logger.log(`Processed ${processed} rows`);
            // Log memory usage
            const memUsage = process.memoryUsage();
            logger.log(`Memory usage: ${Math.round(memUsage.heapUsed / 1024 / 1024)} MB`);
          }
        },
        onError: (error, rowIndex) => {
          logger.error(`Error processing row ${rowIndex}: ${error.message}`);
        },
      },
    );

    logger.log(`Very large file processing completed: ${JSON.stringify(result)}`);
  } catch (error) {
    logger.error(`Very large file processing failed: ${error.message}`);
    throw error;
  }
}

/**
 * Example 4: Processing with custom Transform stream (following your requested pattern)
 */
export async function processWithCustomTransform(
  filePath: string,
  excelService: ExcelStreamProcessorService,
): Promise<void> {
  logger.log('Starting custom transform processing...');

  try {
    // This follows the exact pattern you requested
    const result = await excelService.processExcelFileStream(
      filePath,
      async (row: any, rowIndex: number) => {
        // Your custom processing logic here
        const processedData = {
          originalRow: row,
          processedAt: new Date(),
          rowIndex,
          // Add your custom transformations
          transformedValue: transformRowData(row),
        };

        // Simulate async processing
        await new Promise(resolve => setTimeout(resolve, 1));

        return processedData;
      },
      {
        headerRow: 0,
        skipEmptyRows: true,
        batchSize: 100,
        onProgress: (processed, total) => {
          const percentage = total ? Math.round((processed / total) * 100) : 0;
          logger.log(`Progress: ${processed}/${total} (${percentage}%)`);
        },
      },
    );

    logger.log(`Custom transform processing completed: ${JSON.stringify(result)}`);
  } catch (error) {
    logger.error(`Custom transform processing failed: ${error.message}`);
    throw error;
  }
}

// Helper functions

async function saveStudentRecord(record: StudentRecord): Promise<void> {
  // Simulate database save
  await new Promise(resolve => setTimeout(resolve, 1));
}

async function uploadPinBatch(
  pins: Array<{ pin: string; serialNumber: string }>,
  pinService: BecePinManagementService,
): Promise<void> {
  try {
    const uploadedCount = await pinService.bulkUploadPins(pins, 12.0);
    logger.log(`Uploaded batch of ${uploadedCount} pins`);
  } catch (error) {
    logger.error(`Error uploading pin batch: ${error.message}`);
    throw error;
  }
}

async function processRowData(data: any): Promise<void> {
  // Simulate processing
  await new Promise(resolve => setTimeout(resolve, 1));
}

function transformRowData(row: any): any {
  // Your custom transformation logic
  return {
    processed: true,
    originalKeys: Object.keys(row),
    timestamp: Date.now(),
  };
}

/**
 * Example usage in a NestJS controller or service
 */
export class ExcelProcessingController {
  constructor(
    private readonly excelStreamProcessor: ExcelStreamProcessorService,
    private readonly excelStreamReader: ExcelStreamReaderService,
    private readonly becePinService: BecePinManagementService,
  ) {}

  async uploadLargeExcelFile(filePath: string): Promise<any> {
    try {
      // Get file info first
      const fileInfo = await this.excelStreamProcessor.getExcelFileInfo(filePath);
      logger.log(`Processing file with ${fileInfo.totalSheets} sheets, size: ${fileInfo.fileSize} bytes`);

      // Choose processing method based on file size
      if (fileInfo.fileSize > 100 * 1024 * 1024) { // > 100MB
        // Use row-by-row processing for very large files
        return await processVeryLargeFile(filePath, this.excelStreamReader);
      } else {
        // Use streaming processing for smaller files
        return await processStudentRecords(filePath, this.excelStreamProcessor);
      }
    } catch (error) {
      logger.error(`Excel processing failed: ${error.message}`);
      throw error;
    }
  }
}
