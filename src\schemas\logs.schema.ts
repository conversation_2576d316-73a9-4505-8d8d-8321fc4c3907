import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, SchemaTypes } from 'mongoose';
export type LogsDocument = HydratedDocument<Logs>;
@Schema({
  timestamps: true,
})
export class Logs {
  @Prop({
    type: String,
    default: null,
  })
  event: string;

  @Prop({
    type: SchemaTypes.Mixed,
    required: true,
  })
  request: any;

  @Prop({
    type: SchemaTypes.Mixed,
    default: null,
  })
  response: any;

  @Prop({
    type: String,
    default: null,
  })
  msisdn: string;

  @Prop({
    type: String,
    default: null,
  })
  reference: string;

  @Prop({
    type: String,
    default: 'OK',
  })
  code: string;

  @Prop({
    type: SchemaTypes.Mixed,
    default: null,
  })
  headers: any;

  @Prop({
    type: SchemaTypes.String,
    default: null,
  })
  url: string;
}

export const LogsSchema = SchemaFactory.createForClass(Logs);
