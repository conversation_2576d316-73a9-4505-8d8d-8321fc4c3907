import { UssdStagesDto } from '../dto/ussd-stages.dto';
import { MENU_OPTIONS, MENU_TITLE } from '../../constants/constants';
import { InboundRequestDto } from '../dto/inbound-request.dto';

export const MOCK_SESSION_ID: string = '3883883';
export const CachedUssdMenuMenuStepsMockData = [
  {
    ussdString: `${MENU_TITLE}${MENU_OPTIONS}`,
    previousStep: null,
    currentStep: 1,
    totalPages: 1,
    currentPage: 0,
    acceptedInput: undefined,
  },
];

export const InboundUssdRequestResponseMockData: InboundRequestDto = {
  messageType: '0',
  msisdn: '233550049930',
  sessionId: MOCK_SESSION_ID,
  ussdString: '',
};
