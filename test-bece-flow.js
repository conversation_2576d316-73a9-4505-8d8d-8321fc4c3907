/**
 * Complete BECE Pin Purchase Flow Test
 * Tests the entire flow from USSD to SMS delivery
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/v1';
const TEST_MSISDN = '233501234567';
const TEST_SESSION = `test-session-${Date.now()}`;

async function testCompleteFlow() {
  console.log('🚀 Starting Complete BECE Flow Test...\n');

  try {
    // Step 1: Check available pins
    console.log('📊 Step 1: Checking available pins...');
    const statsResponse = await axios.post(`${BASE_URL}/excel/bece-pins/stats`);
    console.log(`Available pins: ${statsResponse.data.stats.availablePins}`);
    
    if (statsResponse.data.stats.availablePins < 2) {
      console.log('❌ Not enough pins available. Please upload pins first.');
      return;
    }

    // Step 2: Start USSD session
    console.log('\n📱 Step 2: Starting USSD session...');
    let response = await axios.post(`${BASE_URL}/ussd/waec`, {
      sessionId: TEST_SESSION,
      msisdn: TEST_MSISDN,
      ussdString: '',
      network: 'MTN'
    });
    console.log('USSD Response:', response.data.ussdString);

    // Step 3: Select BECE (Option 1)
    console.log('\n🎯 Step 3: Selecting BECE...');
    response = await axios.post(`${BASE_URL}/ussd/waec`, {
      sessionId: TEST_SESSION,
      msisdn: TEST_MSISDN,
      ussdString: '1',
      network: 'MTN'
    });
    console.log('USSD Response:', response.data.ussdString);

    // Step 4: Select SMS delivery (Option 2)
    console.log('\n📨 Step 4: Selecting SMS delivery...');
    response = await axios.post(`${BASE_URL}/ussd/waec`, {
      sessionId: TEST_SESSION,
      msisdn: TEST_MSISDN,
      ussdString: '2',
      network: 'MTN'
    });
    console.log('USSD Response:', response.data.ussdString);

    // Step 5: Enter quantity (2 pins)
    console.log('\n🔢 Step 5: Entering quantity (2)...');
    response = await axios.post(`${BASE_URL}/ussd/waec`, {
      sessionId: TEST_SESSION,
      msisdn: TEST_MSISDN,
      ussdString: '2',
      network: 'MTN'
    });
    console.log('USSD Response:', response.data.ussdString);

    // Step 6: Confirm payment
    console.log('\n💳 Step 6: Confirming payment...');
    response = await axios.post(`${BASE_URL}/ussd/waec`, {
      sessionId: TEST_SESSION,
      msisdn: TEST_MSISDN,
      ussdString: '1',
      network: 'MTN'
    });
    console.log('USSD Response:', response.data.ussdString);
    
    // Extract payment reference from response
    const paymentRef = extractPaymentReference(response.data.ussdString);
    console.log(`Payment Reference: ${paymentRef}`);

    // Step 7: Simulate payment completion
    console.log('\n✅ Step 7: Simulating payment completion...');
    await axios.post(`${BASE_URL}/webhook`, {
      reference: paymentRef,
      status: 'COMPLETED',
      amount: 2400,
      currency: 'GHS',
      customer: {
        email: '<EMAIL>',
        phone: TEST_MSISDN
      },
      metadata: {
        examType: 'BECE',
        quantity: 2
      }
    });
    console.log('✅ Webhook processed successfully!');

    // Step 8: Check final stats
    console.log('\n📊 Step 8: Checking final stats...');
    const finalStats = await axios.post(`${BASE_URL}/excel/bece-pins/stats`);
    console.log(`Available pins after purchase: ${finalStats.data.stats.availablePins}`);

    console.log('\n🎉 Complete flow test successful!');
    console.log('📱 Check SMS delivery to', TEST_MSISDN);
    console.log('🗄️ Check database for status changes');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

function extractPaymentReference(ussdString) {
  // Extract payment reference from USSD response
  // This is a simple implementation - adjust based on actual response format
  const match = ussdString.match(/Reference:\s*([A-Z0-9]+)/i);
  return match ? match[1] : `TEST_REF_${Date.now()}`;
}

// Run the test
testCompleteFlow();
