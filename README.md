# WAEC Results Checker USSD API

A NestJS API for WAEC (West African Examinations Council) results checker PIN purchasing through USSD.

## Features

- USSD interface for purchasing WAEC examination PINs
- Support for different examination types (WASSCE, BECE, NOVDEC, ABCE/GBCE)
- Mobile money payment integration
- PIN generation and delivery
- Session management with Redis
- MongoDB for data persistence

## Project Structure

```
src/
├── constants/     # Application constants
├── exceptions/    # Custom exception filters
├── providers/     # Service providers
├── schemas/       # MongoDB schemas
├── services/      # Business logic services
├── ussd/          # USSD controller and related components
├── utils/         # Utility functions
├── app.module.ts  # Main application module
└── main.ts        # Application entry point
```

## USSD Flow

1. User dials USSD code
2. Main menu is displayed: "Buy Results Checker [1.WASSCE 2.BECE 3.NOVDEC 4.ABCE/GBCE]"
3. User selects exam type
4. User enters quantity of PINs to purchase
5. Payment summary is displayed with options to confirm or cancel
6. On confirmation, payment is processed
7. PINs are generated and delivered to the user

## Installation

```bash
# Install dependencies
$ npm install

# Create .env file from example
$ cp .env.example .env
```

## Configuration

Update the `.env` file with your specific configurations:

```env
# MongoDB
MONGODB_URI=mongodb://localhost:27017/waec
WAEC_DB=waec

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Payment Gateway
PAYMENT_GATEWAY_URL=https://api.payment-gateway.com/v1/payments
PAYMENT_GATEWAY_API_KEY=your-api-key

# Application
PORT=3000
NODE_ENV=development
```

## Running the app

```bash
# development
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

## Testing

```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test coverage
$ npm run test:cov
```

## API Endpoints

### USSD Endpoint

- `POST /api/v1/ussd/waec`: Process USSD requests for WAEC PIN purchases

### Request Format

```json
{
  "msisdn": "233XXXXXXXXX",
  "sessionId": "uniqueSessionIdentifier",
  "messageType": "0",
  "ussdString": "1",
  "network": "MTN"
}
```

## License

This project is [MIT licensed](LICENSE).
