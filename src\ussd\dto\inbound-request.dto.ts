import { IsNotEmpty } from 'class-validator';

export class InboundRequestDto {
  @IsNotEmpty({
    message: 'Msisdn cannot be empty',
  })
  msisdn: string;
  sessionId: string;
  messageType: string;
  ussdString: string;
  network?: string;
}

export const fromInboundUssdRequestToResponsePayload = (
  inboundRequest: InboundRequestDto,
  message: string,
  messageType: string,
) => {
  const inboundResponse = new InboundRequestDto();
  inboundResponse.msisdn = inboundRequest.msisdn;
  inboundResponse.messageType = messageType ?? '0';
  inboundResponse.ussdString = message;
  inboundResponse.sessionId = inboundRequest.sessionId;
  return inboundResponse;
};
