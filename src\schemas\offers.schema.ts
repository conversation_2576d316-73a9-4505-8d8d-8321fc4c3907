import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type OffersDocument = HydratedDocument<Offers>;

export type TStatus = 'AVAILABLE' | 'UNAVAILABLE';
@Schema({
  timestamps: true,
})
export class Offers {
  @Prop()
  network: string;

  @Prop()
  volume: string;

  @Prop({
    default: null,
  })
  unitOfMeasure: string;

  @Prop({
    type: Number,
    default: 0,
  })
  price: number;

  @Prop({
    type: String,
    default: 'AVAILABLE',
  })
  status: TStatus;
}

export const OfferSchema = SchemaFactory.createForClass(Offers);
