NODE_ENV=dev

MONGODB_URL=mongodb://localhost:27017/resultsChecker
MONGODB_USERNAME=
MONGODB_PASSWORD=

REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_USER=
REDIS_PASSWORD=

PORT=3000

# Payment configuration
PAYMENT_BASE_URL=https://payment-api.example.com
PAYMENT_USERNAME=your-payment-username
PAYMENT_PASSWORD=your-payment-password
PAYMENT_CALLBACK_ID=callback123

# Data service configuration
OVALDATAGH_BASE_URL=https://api.ovaldatagh.com
OVALDATAGH_API_KEY=your-ovaldata-api-key

# Email configuration (SendGrid)
SENDGRID_API_KEY=your-sendgrid-api-key
EMAIL_FROM=<EMAIL>

# SMS configuration
SMS_API_URL=https://sms-api.example.com/send
SMS_API_KEY=your-sms-api-key
SMS_SENDER=KAIROS