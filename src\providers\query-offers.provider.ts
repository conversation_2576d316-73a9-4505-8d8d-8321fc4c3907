import { Inject, Injectable } from '@nestjs/common';
import { RedisCache } from '@tirke/node-cache-manager-ioredis';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { InjectModel } from '@nestjs/mongoose';
import { Offers, OffersDocument, TStatus } from '../schemas/offers.schema';
import { Model } from 'mongoose';
import { APP_NAME, ITEMS_PER_PAGE } from '../constants/constants';
import { chunk } from '../utils/helpers';
import { logger } from '../utils/logging';

@Injectable()
export class QueryOffersProvider {
  private readonly hashMap: Map<string, any>;
  constructor(
    @Inject(CACHE_MANAGER) private readonly cacheRepository: RedisCache,
    @InjectModel(Offers.name)
    private readonly offerModel: Model<OffersDocument>,
  ) {
    this.hashMap = new Map();
  }

  public findAll = async (network: string, status?: TStatus) => {
    let cachedOffers: OffersDocument[];
    cachedOffers = await this.cacheRepository.get(
      `${APP_NAME}:offers:${network}`,
    );
    if (!cachedOffers) {
      cachedOffers = await this.offerModel
        .find({ status: status ?? 'AVAILABLE', network })
        .lean()
        .exec();
      logger.info(
        '%s ::: Query offers ::: %s',
        QueryOffersProvider.name,
        JSON.stringify(cachedOffers),
      );
      await this.cacheRepository.set(
        `${APP_NAME}:offers:${network}`,
        cachedOffers ?? [],
      );
    }
    logger.info(
      '%s ::: Cached Offers ::: %s',
      QueryOffersProvider.name,
      JSON.stringify(cachedOffers),
    );

    this.handleGenerateCachedOffersPages(cachedOffers);
    return { cachedOffers, hashMap: this.hashMap };
  };

  public getCachedPagedOffers = async (key: string, network?: string) => {
    if (!this.hashMap.get(key)) {
      const cachedOffers: OffersDocument[] = await this.cacheRepository.get(
        `${APP_NAME}:offers:${network}`,
      );
      this.handleGenerateCachedOffersPages(cachedOffers);
      return this.hashMap.get(key);
    }
    return this.hashMap.get(key);
  };

  public getCachedTotalPages = () => this.hashMap.size;

  private handleGenerateCachedOffersPages(cachedOffers: OffersDocument[]) {
    chunk<OffersDocument>(cachedOffers, ITEMS_PER_PAGE).forEach(
      (chunk, index) => {
        logger.info(
          '%s ::: Chunked offer documents ::: %s',
          QueryOffersProvider.name,
          JSON.stringify(chunk),
        );
        const page = index + 1;
        logger.info(`%s ::: Page ::: %s`, QueryOffersProvider.name, page);
        this.hashMap.set(
          `${page}`,
          chunk.map(({ unitOfMeasure, volume, price }, idx) => {
            const nextItem = idx + 1;
            return `${page > 1 ? (page - 1) * ITEMS_PER_PAGE + nextItem : nextItem})${volume}${unitOfMeasure} @ GHC${price}`;
          }),
        );
      },
    );
  }
}
