import { Body, Controller, HttpCode, HttpStatus, Post } from '@nestjs/common';
import { UssdService } from './ussd.service';
import { InboundRequestDto } from './dto/inbound-request.dto';

@Controller('ussd')
export class UssdController {
  constructor(private readonly ussdService: UssdService) {}

  @Post('waec')
  @HttpCode(HttpStatus.OK)
  async handleWaecPinPurchase(@Body() requestPayload: InboundRequestDto) {
    return this.ussdService.processInboundRequest(requestPayload);
  }
}
