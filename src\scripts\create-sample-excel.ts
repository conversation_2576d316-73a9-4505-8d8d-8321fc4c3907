/**
 * Standalone script to create sample Excel files for testing
 * No database connection required
 */

import * as XLSX from 'xlsx';
import * as path from 'path';

/**
 * Generate a random 10-digit PIN
 */
function generateRandomPin(): string {
  return Math.floor(1000000000 + Math.random() * 9000000000).toString();
}

/**
 * Create a sample Excel file with B.E.C.E pins
 */
function createSampleExcelFile(fileName: string, pinCount: number): string {
  console.log(`Creating sample Excel file with ${pinCount} pins...`);

  const workbook = XLSX.utils.book_new();
  const data = [];

  // Add header row
  data.push(['PIN', 'Serial Number']);

  // Generate test pins
  for (let i = 1; i <= pinCount; i++) {
    const pin = generateRandomPin();
    const serialNumber = `BECE${String(i).padStart(6, '0')}`;
    data.push([pin, serialNumber]);
  }

  const worksheet = XLSX.utils.aoa_to_sheet(data);
  XLSX.utils.book_append_sheet(workbook, worksheet, 'BECEPins');

  const filePath = path.join(process.cwd(), fileName);
  XLSX.writeFile(workbook, filePath);

  console.log(`✅ Sample Excel file created: ${filePath}`);
  return filePath;
}

/**
 * Create a sample CSV file with B.E.C.E pins
 */
function createSampleCsvFile(fileName: string, pinCount: number): string {
  console.log(`Creating sample CSV file with ${pinCount} pins...`);

  const lines = ['PIN,Serial Number'];

  // Generate test pins
  for (let i = 1; i <= pinCount; i++) {
    const pin = generateRandomPin();
    const serialNumber = `BECE${String(i).padStart(6, '0')}`;
    lines.push(`${pin},${serialNumber}`);
  }

  const filePath = path.join(process.cwd(), fileName);
  const fs = require('fs');
  fs.writeFileSync(filePath, lines.join('\n'));

  console.log(`✅ Sample CSV file created: ${filePath}`);
  return filePath;
}

/**
 * Create a sample JSON file with B.E.C.E pins
 */
function createSampleJsonFile(fileName: string, pinCount: number): string {
  console.log(`Creating sample JSON file with ${pinCount} pins...`);

  const pins = [];

  // Generate test pins
  for (let i = 1; i <= pinCount; i++) {
    const pin = generateRandomPin();
    const serialNumber = `BECE${String(i).padStart(6, '0')}`;
    pins.push({ pin, serialNumber });
  }

  const filePath = path.join(process.cwd(), fileName);
  const fs = require('fs');
  fs.writeFileSync(filePath, JSON.stringify(pins, null, 2));

  console.log(`✅ Sample JSON file created: ${filePath}`);
  return filePath;
}

/**
 * Create a large Excel file for memory testing
 */
function createLargeExcelFile(fileName: string, rowCount: number): string {
  console.log(`Creating large Excel file with ${rowCount} rows for memory testing...`);

  const workbook = XLSX.utils.book_new();
  const data = [];

  // Add header row
  data.push(['ID', 'Name', 'Email', 'Value', 'Date', 'Description']);

  // Generate test data in batches to avoid memory issues
  const batchSize = 10000;
  let currentRow = 1;

  while (currentRow <= rowCount) {
    const batchEnd = Math.min(currentRow + batchSize - 1, rowCount);
    
    for (let i = currentRow; i <= batchEnd; i++) {
      data.push([
        `ID${String(i).padStart(8, '0')}`,
        `Test User ${i}`,
        `user${i}@example.com`,
        Math.floor(Math.random() * 1000),
        new Date().toISOString(),
        `Description for row ${i} with some additional text to make it longer`,
      ]);
    }

    console.log(`Generated ${batchEnd} rows...`);
    currentRow = batchEnd + 1;
  }

  const worksheet = XLSX.utils.aoa_to_sheet(data);
  XLSX.utils.book_append_sheet(workbook, worksheet, 'TestData');

  const filePath = path.join(process.cwd(), fileName);
  XLSX.writeFile(workbook, filePath);

  console.log(`✅ Large Excel file created: ${filePath}`);
  return filePath;
}

// Main execution
function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  try {
    switch (command) {
      case 'excel':
        if (args.length < 2) {
          console.error('Usage: yarn create-sample excel <count> [filename]');
          process.exit(1);
        }
        const excelCount = parseInt(args[1]);
        const excelFileName = args[2] || `sample-bece-pins-${excelCount}.xlsx`;
        createSampleExcelFile(excelFileName, excelCount);
        break;

      case 'csv':
        if (args.length < 2) {
          console.error('Usage: yarn create-sample csv <count> [filename]');
          process.exit(1);
        }
        const csvCount = parseInt(args[1]);
        const csvFileName = args[2] || `sample-bece-pins-${csvCount}.csv`;
        createSampleCsvFile(csvFileName, csvCount);
        break;

      case 'json':
        if (args.length < 2) {
          console.error('Usage: yarn create-sample json <count> [filename]');
          process.exit(1);
        }
        const jsonCount = parseInt(args[1]);
        const jsonFileName = args[2] || `sample-bece-pins-${jsonCount}.json`;
        createSampleJsonFile(jsonFileName, jsonCount);
        break;

      case 'large':
        if (args.length < 2) {
          console.error('Usage: yarn create-sample large <row-count> [filename]');
          process.exit(1);
        }
        const largeCount = parseInt(args[1]);
        const largeFileName = args[2] || `large-test-file-${largeCount}.xlsx`;
        createLargeExcelFile(largeFileName, largeCount);
        break;

      case 'all':
        const count = args[1] ? parseInt(args[1]) : 100;
        console.log(`Creating all sample file types with ${count} records...`);
        createSampleExcelFile(`sample-bece-pins-${count}.xlsx`, count);
        createSampleCsvFile(`sample-bece-pins-${count}.csv`, count);
        createSampleJsonFile(`sample-bece-pins-${count}.json`, count);
        console.log('✅ All sample files created successfully!');
        break;

      default:
        console.log('Sample File Creator - No Database Required');
        console.log('');
        console.log('Available commands:');
        console.log('  excel <count> [filename]     - Create Excel file with B.E.C.E pins');
        console.log('  csv <count> [filename]       - Create CSV file with B.E.C.E pins');
        console.log('  json <count> [filename]      - Create JSON file with B.E.C.E pins');
        console.log('  large <count> [filename]     - Create large Excel file for memory testing');
        console.log('  all <count>                  - Create all file types');
        console.log('');
        console.log('Examples:');
        console.log('  yarn create-sample excel 100');
        console.log('  yarn create-sample csv 1000 my-pins.csv');
        console.log('  yarn create-sample json 500');
        console.log('  yarn create-sample large 50000');
        console.log('  yarn create-sample all 100');
        break;
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

main();
