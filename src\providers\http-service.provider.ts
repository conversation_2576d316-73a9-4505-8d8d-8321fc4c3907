import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { AxiosError, AxiosResponse } from 'axios';
import { catchError, map, of, tap } from 'rxjs';
import { logger } from '../utils/logging';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { RedisCache } from '@tirke/node-cache-manager-ioredis';
import { IAPIResponse } from '../types';

@Injectable()
export class HttpServiceProvider {
  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    @Inject(CACHE_MANAGER) private readonly cacheRepository: RedisCache,
  ) {}

  public get = <B = any, H = Record<string, any>, R = any>(
    url: string,
    body: B,
    headers?: H,
  ) => {
    return this.httpService
      .get(url, {
        headers: {
          ...headers,
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
      })
      .pipe(
        tap(() =>
          logger.info(
            'Logging the request to the callback api here :::: %s',
            JSON.stringify(body),
          ),
        ),
        map(
          ({
            data,
            status,
            statusText,
            headers,
          }: AxiosResponse<IAPIResponse<R>>) => {
            logger.info(
              '%s ::: Successful response from callback request ::: %s ::: %s ::: %s ::: %s',
              status,
              statusText,
              headers,
              data,
            );
            return data;
          },
        ),
        catchError((err: AxiosError) => {
          logger.error(err?.message);
          // this.cacheRepository.set(
          //   `${USSD_SESSION_ERRORS}${formattedServiceCode}:${new Date().getTime()}`,
          //   {
          //     code: err?.code,
          //     message: {
          //       rawMessage: err?.message,
          //       statusMessage: err?.response?.statusText,
          //     },
          //     data: err?.response?.data,
          //     endpoint: err?.response?.config?.url,
          //     headers: err?.response?.headers,
          //   },
          // );
          return of({
            statusCode: '5000',
            statusMessage: err.message,
            err,
            timestamp: new Date().getTime(),
            path: '/ussd',
          });
        }),
      );
  };

  public post = <B = any, H = Record<string, any>, R = any>(
    url: string,
    body: B,
    headers?: H,
  ) => {
    return this.httpService
      .post(url, body, {
        headers: {
          ...headers,
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
      })
      .pipe(
        tap(() =>
          logger.info(
            'Logging the request to the callback api here :::: %s',
            JSON.stringify(body),
          ),
        ),
        map(({ data, statusText, headers }: AxiosResponse<IAPIResponse<R>>) => {
          logger.info(
            '%s ::: Status ::: %s ::: Payment Response from the Payment API ::: %s ::: headers --> %s',
            HttpServiceProvider.name,
            statusText,
            JSON.stringify(data),
            headers,
          );
          return data;
        }),
        catchError((err: AxiosError) => {
          logger.error(err?.message);
          // this.cacheRepository.set(
          //   `${USSD_SESSION_ERRORS}${formattedServiceCode}:${new Date().getTime()}`,
          //   {
          //     code: err?.code,
          //     message: {
          //       rawMessage: err?.message,
          //       statusMessage: err?.response?.statusText,
          //     },
          //     data: err?.response?.data,
          //     endpoint: err?.response?.config?.url,
          //     headers: err?.response?.headers,
          //   },
          // );
          return of({
            statusCode: '5000',
            statusMessage: err.message,
            data: null,
            timestamp: new Date().getTime(),
            path: '/ussd',
          });
        }),
      );
  };
}
