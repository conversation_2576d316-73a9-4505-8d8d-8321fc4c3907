import { Module } from '@nestjs/common';
import { UssdController } from './ussd.controller';
import { UssdService } from './ussd.service';
import { MongooseModule } from '@nestjs/mongoose';
import { UssdStageInputProvider } from '../providers/ussd-stage-input.provider';
import { HttpModule } from '@nestjs/axios';
import { Logs, LogsSchema } from '../schemas/logs.schema';
import { WaecPin, WaecPinSchema } from '../schemas/waec-pin.schema';
import {
  WaecTransaction,
  WaecTransactionSchema,
} from '../schemas/waec-transaction.schema';

import { UssdFlowCompletionProvider } from '../providers/ussd-flow-completion.provider';
import { QueryOffersProvider } from '../providers/query-offers.provider';
import { Purchases, PurchaseSchema } from '../schemas/purchases.schema';
import { Offers, OfferSchema } from '../schemas/offers.schema';
import { NotificationProvider } from '../providers/notification.provider';
import { BecePinManagementService } from '../services/bece-pin-management.service';

@Module({
  imports: [
    HttpModule,
    MongooseModule.forFeature([
      { name: WaecPin.name, schema: WaecPinSchema },
      { name: WaecTransaction.name, schema: WaecTransactionSchema },

      { name: Logs.name, schema: LogsSchema },
      { name: Purchases.name, schema: PurchaseSchema },
      { name: Offers.name, schema: OfferSchema },
    ]),
  ],
  controllers: [UssdController],
  providers: [
    UssdService,
    UssdStageInputProvider,
    UssdFlowCompletionProvider,
    QueryOffersProvider,
    NotificationProvider,
    BecePinManagementService,
  ],
})
export class UssdModule { }
