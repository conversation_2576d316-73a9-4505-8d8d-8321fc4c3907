import { createLogger, format, transports } from 'winston';

/**
 * Custom logger for all Cleva APIs
 * <AUTHOR> <<<EMAIL>>>
 * @returns @{winston.Logger}
 */
export const logger = createLogger({
  transports: [
    new transports.Console({
      level: 'info',
      format: format.combine(
        format.simple(),
        format.timestamp(),
        format.splat(),
        format.json(),
      ),
    }),
  ],
});
