/**
 * This is a utility script to mock generating WAEC pins for testing purposes.
 *
 * In a real implementation, pin generation would likely be part of a separate secure system
 * or integrated with an actual WAEC service.
 */

import { MongooseModule } from '@nestjs/mongoose';
import { NestFactory } from '@nestjs/core';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { WaecPin, WaecPinSchema } from '../schemas/waec-pin.schema';
import {
  WaecTransaction,
  WaecTransactionSchema,
} from '../schemas/waec-transaction.schema';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { Injectable } from '@nestjs/common';
import { generateRandomString } from '../utils/helpers';

@Injectable()
class PinGeneratorService {
  constructor(
    @InjectModel(WaecPin.name)
    private readonly waecPinModel: Model<WaecPin>,
    @InjectModel(WaecTransaction.name)
    private readonly waecTransactionModel: Model<WaecTransaction>,
    private readonly configService: ConfigService,
  ) {}

  async generatePinsForTransaction(transactionId: string): Promise<void> {
    console.log(`Generating pins for transaction: ${transactionId}`);

    // Find the transaction
    const transaction = await this.waecTransactionModel.findOne({
      transactionId,
      paymentStatus: 'COMPLETED',
      pinsGenerated: false,
    });

    if (!transaction) {
      console.log(
        `Transaction ${transactionId} not found or not ready for pin generation`,
      );
      return;
    }

    // Generate pins based on quantity
    const pins = [];
    for (let i = 0; i < transaction.quantity; i++) {
      pins.push({
        pin: generateRandomString(12),
        serialNumber: generateRandomString(10, true),
        examType: transaction.examType,
        price: transaction.unitPrice,
        purchasedBy: transaction.msisdn,
        msisdn: transaction.msisdn,
        email: transaction.email,
        transactionId: transaction.transactionId,
        network: transaction.network,
      });
    }

    // Save pins to database
    await this.waecPinModel.insertMany(pins);

    // Update transaction to indicate pins have been generated
    await this.waecTransactionModel.updateOne(
      { transactionId },
      { pinsGenerated: true },
    );

    console.log(
      `Generated ${pins.length} pins for transaction ${transactionId}`,
    );
    console.log('Pins:', pins);
  }

  async runForRecentTransactions(): Promise<void> {
    // Find recent completed transactions without pins
    const transactions = await this.waecTransactionModel.find({
      paymentStatus: 'COMPLETED',
      pinsGenerated: false,
    });

    console.log(
      `Found ${transactions.length} transactions to generate pins for`,
    );

    for (const transaction of transactions) {
      await this.generatePinsForTransaction(transaction.transactionId);
    }
  }
}

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>('MONGODB_URI'),
      }),
      inject: [ConfigService],
    }),
    MongooseModule.forFeature([
      { name: WaecPin.name, schema: WaecPinSchema },
      { name: WaecTransaction.name, schema: WaecTransactionSchema },
    ]),
  ],
  providers: [PinGeneratorService],
})
class AppModule {}

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const service = app.get(PinGeneratorService);

  // Run the pin generator for recent transactions
  await service.runForRecentTransactions();

  // Or generate for a specific transaction
  // await service.generatePinsForTransaction('specific-transaction-id');

  await app.close();
}

bootstrap()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('Error in pin generation script:', error);
    process.exit(1);
  });
