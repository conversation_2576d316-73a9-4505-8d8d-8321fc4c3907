import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ExcelStreamProcessorService } from '../services/excel-stream-processor.service';
import { ExcelStreamReaderService } from '../services/excel-stream-reader.service';
import { BecePinManagementService } from '../services/bece-pin-management.service';
import { WaecPin, WaecPinSchema } from '../schemas/waec-pin.schema';
import {
  WaecTransaction,
  WaecTransactionSchema,
} from '../schemas/waec-transaction.schema';
import { ExcelProcessingController } from './excel-processing.controller';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: WaecPin.name, schema: WaecPinSchema },
      { name: WaecTransaction.name, schema: WaecTransactionSchema },
    ]),
  ],
  controllers: [ExcelProcessingController],
  providers: [
    ExcelStreamProcessorService,
    ExcelStreamReaderService,
    BecePinManagementService,
  ],
  exports: [
    ExcelStreamProcessorService,
    ExcelStreamReaderService,
    BecePinManagementService,
  ],
})
export class ExcelProcessingModule { }
