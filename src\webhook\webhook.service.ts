import { Injectable } from '@nestjs/common';
import { IncomingPaymentCompletionDto } from './dto/incoming-payment-completion.dto';
import { logger } from '../utils/logging';
import { InjectModel } from '@nestjs/mongoose';
import { Purchases, PurchasesDocument } from '../schemas/purchases.schema';
import { Model } from 'mongoose';
import { Logs, LogsDocument } from '../schemas/logs.schema';
import { HttpService } from '@nestjs/axios';
import { IOvalDataResponse } from '../types';
import { ConfigService } from '@nestjs/config';
import { map, mergeMap, tap } from 'rxjs';
import {
  Transaction,
  TransactionDocument,
} from '../schemas/transaction.schema';
import { NotificationProvider } from '../providers/notification.provider';
import {
  WaecTransaction,
  WaecTransactionDocument,
} from '../schemas/waec-transaction.schema';
import { BecePinManagementService } from '../services/bece-pin-management.service';

@Injectable()
export class WebhookService {
  constructor(
    @InjectModel(Purchases.name)
    private readonly purchaseModel: Model<PurchasesDocument>,
    @InjectModel(Logs.name)
    private readonly logsModel: Model<LogsDocument>,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    @InjectModel(Transaction.name)
    private readonly transactionModel: Model<TransactionDocument>,
    @InjectModel(WaecTransaction.name)
    private readonly waecTransactionModel: Model<WaecTransactionDocument>,
    private readonly notificationProvider: NotificationProvider,
    private readonly becePinManagementService: BecePinManagementService,
  ) { }

  async incomingRequest(body: IncomingPaymentCompletionDto): Promise<void> {
    logger.info(
      '%s ::: Incoming webhook payment after payment completion ::: %s',
      WebhookService.name,
      JSON.stringify(body),
    );

    // Check for both normal purchases and WAEC purchases
    const [purchase, transaction, waecTransaction] = await Promise.all([
      this.purchaseModel
        .findOne({ paymentReference: body.reference, status: 'STARTED' })
        .lean()
        .exec(),
      this.transactionModel.findOne({
        reference: body.reference,
        status: 'COMPLETED',
      }),
      this.waecTransactionModel.findOne({
        paymentReference: body.reference,
        paymentStatus: 'PENDING',
      }),
    ]);

    // Log the request if no matching purchase or transaction found
    if ((!purchase && !waecTransaction) || !transaction) {
      logger.info(
        '%s ::: Incoming payment reference does not exist or has already been used ::: %s',
        WebhookService.name,
        JSON.stringify(body),
      );
      await this.logsModel.create({
        response: body,
        event: 'WEBHOOK',
        code: 'OK',
        reference: body?.reference,
      });
      return;
    }

    logger.info(
      '%s :: Purchase Data From DB -> %s, Paid Record -> %s, WAEC Transaction -> %s',
      WebhookService.name,
      JSON.stringify(purchase),
      JSON.stringify(transaction),
      JSON.stringify(waecTransaction),
    );

    switch (body.status?.toUpperCase()) {
      case 'COMPLETED':
      case 'SUCCESS': {
        // Handle normal purchase (data packages)
        if (purchase) {
          const response = await this.purchaseModel.findOneAndUpdate(
            {
              paymentReference: body.reference,
            },
            { status: 'COMPLETED' },
          );
          logger.info(
            '%s ::: [SUCCESS] Webhook saved successfully ::: %s',
            WebhookService.name,
            JSON.stringify(response),
          );
          // fire data crediting
          const payload = {
            network: response.offer.network?.toLowerCase(),
            volume: response.offer.volume,
            recipient: response.recipientMsisdn,
            reference: response.paymentReference,
          };
          logger.info(
            '%s ::: Request Payload to OVALDATAGH :: %s',
            WebhookService.name,
            JSON.stringify(payload),
          );
          this.httpService
            .post<IOvalDataResponse>(
              `${this.configService.get<string>('OVALDATAGH_BASE_URL')}/v1/transactions/initialize`,
              payload,
              {
                headers: {
                  'Content-Type': 'application/json',
                  Authorization: `Bearer ${this.configService.get<string>('OVALDATAGH_API_KEY')}`,
                },
              },
            )
            .pipe(
              tap((val) =>
                logger.info(
                  '%s ::: Response from OVALDATAGH API ::: %s',
                  WebhookService.name,
                  JSON.stringify(val),
                ),
              ),
            )
            .subscribe(
              async ({ data, status, statusText, headers, config }) => {
                logger.info(
                  '%s ::: Successful API Subscription for Oval data gh ::: Status ::: %s ::: StatusText ::: %s ::: Data ::: %s',
                  WebhookService.name,
                  status,
                  statusText,
                  JSON.stringify(data),
                );
                await this.logsModel.create({
                  event: 'OVALDATAGH',
                  request: payload,
                  response: data,
                  msisdn: payload.recipient,
                  reference: data.data?.reference,
                  url: config.url,
                  headers,
                });
                if (data.code === '0000') {
                } else {
                }
              },
            );
        }

        // Handle WAEC transaction
        if (waecTransaction) {
          logger.info(
            '%s ::: [SUCCESS] Processing WAEC transaction ::: %s',
            WebhookService.name,
            waecTransaction.transactionId,
          );

          // Update transaction status to COMPLETED
          await this.waecTransactionModel.updateOne(
            { paymentReference: body.reference },
            {
              paymentStatus: 'COMPLETED',
            },
          );

          // Handle B.E.C.E pin allocation if exam type is BECE
          if (waecTransaction.examType === 'BECE') {
            try {
              logger.info(
                '%s ::: Allocating B.E.C.E pins for transaction ::: %s',
                WebhookService.name,
                waecTransaction.transactionId,
              );

              // Allocate pins from the pre-uploaded collection
              await this.becePinManagementService.allocatePinsForTransaction(
                waecTransaction.transactionId,
                waecTransaction.quantity,
              );

              // Update transaction to indicate pins are generated
              await this.waecTransactionModel.updateOne(
                { paymentReference: body.reference },
                {
                  pinsGenerated: true,
                },
              );

              logger.info(
                '%s ::: Successfully allocated B.E.C.E pins for transaction ::: %s',
                WebhookService.name,
                waecTransaction.transactionId,
              );
            } catch (error) {
              logger.error(
                '%s ::: Error allocating B.E.C.E pins for transaction ::: %s ::: %s',
                WebhookService.name,
                waecTransaction.transactionId,
                error.message,
              );
              // Don't return here - still try to send notification with error info
            }
          } else {
            // For other exam types (WASSCE, NOVDEC, ABCE/GBCE), use existing logic
            await this.waecTransactionModel.updateOne(
              { paymentReference: body.reference },
              {
                pinsGenerated: true,
              },
            );
          }

          // Send notification with voucher details
          try {
            await this.notificationProvider.sendNotification(
              waecTransaction.transactionId,
            );

            // Mark B.E.C.E pins as delivered if applicable
            if (waecTransaction.examType === 'BECE') {
              await this.becePinManagementService.markPinsAsDelivered(
                waecTransaction.transactionId,
              );
            }

            logger.info(
              '%s ::: Notification sent for WAEC transaction ::: %s',
              WebhookService.name,
              waecTransaction.transactionId,
            );
          } catch (error) {
            logger.error(
              '%s ::: Error sending notification for WAEC transaction ::: %s ::: %s',
              WebhookService.name,
              waecTransaction.transactionId,
              error.message,
            );
          }
        }

        return;
      }
      case 'FAILED': {
        // Handle failed purchase
        if (purchase) {
          const response = await this.purchaseModel
            .updateOne(
              { paymentReference: body.reference },
              { $set: { status: 'FAILED' } },
            )
            .exec();
          logger.info(
            '%s :::[FAILED] Webhook saved successfully ::: %s',
            WebhookService.name,
            JSON.stringify(response),
          );
        }

        // Handle failed WAEC transaction
        if (waecTransaction) {
          await this.waecTransactionModel.updateOne(
            { paymentReference: body.reference },
            { paymentStatus: 'FAILED' },
          );
          logger.info(
            '%s :::[FAILED] WAEC transaction updated ::: %s',
            WebhookService.name,
            waecTransaction.transactionId,
          );
        }

        return;
      }
    }
  }
}
