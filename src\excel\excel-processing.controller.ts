import {
  Controller,
  Post,
  UploadedFile,
  UseInterceptors,
  BadRequestException,
  Logger,
  Body,
  Query,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ExcelStreamProcessorService } from '../services/excel-stream-processor.service';
import { ExcelStreamReaderService } from '../services/excel-stream-reader.service';
import { BecePinManagementService } from '../services/bece-pin-management.service';
import * as fs from 'fs';
import * as path from 'path';

interface UploadedFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  buffer: Buffer;
  filename?: string;
  path?: string;
}

interface UploadResponse {
  success: boolean;
  message: string;
  stats?: {
    totalProcessed: number;
    successCount: number;
    errorCount: number;
    processingTime: number;
  };
  errors?: Array<{ rowIndex: number; error: string }>;
}

@Controller('excel')
export class ExcelProcessingController {
  private readonly logger = new Logger(ExcelProcessingController.name);

  constructor(
    private readonly excelStreamProcessor: ExcelStreamProcessorService,
    private readonly excelStreamReader: ExcelStreamReaderService,
    private readonly becePinService: BecePinManagementService,
  ) { }

  /**
   * Upload and process B.E.C.E pins from Excel file
   */
  @Post('upload-bece-pins')
  @UseInterceptors(FileInterceptor('file'))
  async uploadBecePins(
    @UploadedFile() file: UploadedFile,
    @Body('price') price: string,
    @Query('batchSize') batchSize?: string,
  ): Promise<UploadResponse> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    if (!price || isNaN(parseFloat(price))) {
      throw new BadRequestException('Valid price is required');
    }

    const pinPrice = parseFloat(price);
    const processingBatchSize = batchSize ? parseInt(batchSize) : 1000;

    this.logger.log(`Processing B.E.C.E pins upload: ${file.originalname}, Price: ${pinPrice}`);

    // Save uploaded file temporarily
    const tempFilePath = path.join(process.cwd(), 'temp', `${Date.now()}-${file.originalname}`);

    try {
      // Ensure temp directory exists
      const tempDir = path.dirname(tempFilePath);
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      // Write file to disk
      fs.writeFileSync(tempFilePath, file.buffer);

      // Get file info
      const fileInfo = await this.excelStreamProcessor.getExcelFileInfo(tempFilePath);
      this.logger.log(`File info: ${JSON.stringify(fileInfo)}`);

      const startTime = Date.now();
      const pinsToUpload: Array<{ pin: string; serialNumber: string }> = [];
      let uploadedTotal = 0;

      // Process the Excel file
      const result = await this.excelStreamReader.processExcelWithTransform(
        tempFilePath,
        async (row: any, _rowIndex: number) => {
          const pinRecord = {
            pin: (row['PIN'] || row.A || '').toString().trim(),
            serialNumber: (row['Serial Number'] || row['Serial'] || row.B || '').toString().trim(),
          };

          // Validate pin data
          if (!pinRecord.pin || !pinRecord.serialNumber) {
            throw new Error(`Missing PIN or Serial Number`);
          }

          // Validate PIN format (10 digits)
          if (!/^\d{10}$/.test(pinRecord.pin)) {
            throw new Error(`Invalid PIN format: ${pinRecord.pin}`);
          }

          pinsToUpload.push(pinRecord);

          // Upload in batches to avoid memory buildup
          if (pinsToUpload.length >= processingBatchSize) {
            try {
              const uploaded = await this.becePinService.bulkUploadPins([...pinsToUpload], pinPrice);
              uploadedTotal += uploaded;
              this.logger.log(`Uploaded batch: ${uploaded} pins (Total: ${uploadedTotal})`);
            } catch (uploadError) {
              this.logger.error(`Batch upload error: ${uploadError.message}`);
              throw uploadError;
            }
            pinsToUpload.length = 0; // Clear array
          }

          return pinRecord;
        },
        {
          headerRow: 0,
          skipEmptyRows: true,
          onProgress: (processed) => {
            if (processed % 1000 === 0) {
              this.logger.log(`Processed ${processed} pins`);
            }
          },
          onError: (error, rowIndex) => {
            this.logger.error(`Pin processing error at row ${rowIndex}: ${error.message}`);
          },
        },
      );

      // Upload remaining pins
      if (pinsToUpload.length > 0) {
        try {
          const uploaded = await this.becePinService.bulkUploadPins(pinsToUpload, pinPrice);
          uploadedTotal += uploaded;
          this.logger.log(`Uploaded final batch: ${uploaded} pins (Total: ${uploadedTotal})`);
        } catch (uploadError) {
          this.logger.error(`Final batch upload error: ${uploadError.message}`);
          throw uploadError;
        }
      }

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      this.logger.log(`B.E.C.E pins upload completed. Total uploaded: ${uploadedTotal}`);

      return {
        success: true,
        message: `Successfully processed ${result.totalProcessed} pins. Uploaded: ${uploadedTotal}`,
        stats: {
          totalProcessed: result.totalProcessed,
          successCount: result.successCount,
          errorCount: result.errorCount,
          processingTime,
        },
        errors: result.errors.slice(0, 10), // Return first 10 errors
      };
    } catch (error) {
      this.logger.error(`B.E.C.E pins upload failed: ${error.message}`, error.stack);
      return {
        success: false,
        message: `Upload failed: ${error.message}`,
      };
    } finally {
      // Clean up temporary file
      if (fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath);
      }
    }
  }

  /**
   * Process any Excel file with custom processing logic
   */
  @Post('process-file')
  @UseInterceptors(FileInterceptor('file'))
  async processExcelFile(
    @UploadedFile() file: UploadedFile,
    @Query('method') method: 'stream' | 'transform' | 'rowByRow' = 'transform',
    @Query('batchSize') batchSize?: string,
    @Query('maxRows') maxRows?: string,
  ): Promise<UploadResponse> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    const processingBatchSize = batchSize ? parseInt(batchSize) : 1000;
    const maxRowsLimit = maxRows ? parseInt(maxRows) : undefined;

    this.logger.log(`Processing Excel file: ${file.originalname}, Method: ${method}`);

    // Save uploaded file temporarily
    const tempFilePath = path.join(process.cwd(), 'temp', `${Date.now()}-${file.originalname}`);

    try {
      // Ensure temp directory exists
      const tempDir = path.dirname(tempFilePath);
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      // Write file to disk
      fs.writeFileSync(tempFilePath, file.buffer);

      const startTime = Date.now();
      let result: any;

      // Choose processing method
      switch (method) {
        case 'stream':
          result = await this.excelStreamProcessor.processExcelFileStream(
            tempFilePath,
            this.processRow.bind(this),
            {
              batchSize: processingBatchSize,
              maxRows: maxRowsLimit,
              onProgress: this.logProgress.bind(this),
            },
          );
          break;

        case 'transform':
          result = await this.excelStreamReader.processExcelWithTransform(
            tempFilePath,
            this.processRow.bind(this),
            {
              batchSize: processingBatchSize,
              maxRows: maxRowsLimit,
              onProgress: this.logProgress.bind(this),
            },
          );
          break;

        case 'rowByRow':
          result = await this.excelStreamReader.processExcelRowByRow(
            tempFilePath,
            this.processRow.bind(this),
            {
              maxRows: maxRowsLimit,
              onProgress: this.logProgress.bind(this),
            },
          );
          break;

        default:
          throw new BadRequestException('Invalid processing method');
      }

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      this.logger.log(`Excel processing completed using ${method} method`);

      return {
        success: true,
        message: `Successfully processed ${result.totalProcessed} rows using ${method} method`,
        stats: {
          totalProcessed: result.totalProcessed,
          successCount: result.successCount,
          errorCount: result.errorCount,
          processingTime,
        },
        errors: result.errors.slice(0, 10), // Return first 10 errors
      };
    } catch (error) {
      this.logger.error(`Excel processing failed: ${error.message}`, error.stack);
      return {
        success: false,
        message: `Processing failed: ${error.message}`,
      };
    } finally {
      // Clean up temporary file
      if (fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath);
      }
    }
  }

  /**
   * Get Excel file information without processing
   */
  @Post('file-info')
  @UseInterceptors(FileInterceptor('file'))
  async getFileInfo(@UploadedFile() file: UploadedFile): Promise<any> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    // Save uploaded file temporarily
    const tempFilePath = path.join(process.cwd(), 'temp', `${Date.now()}-${file.originalname}`);

    try {
      // Ensure temp directory exists
      const tempDir = path.dirname(tempFilePath);
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      // Write file to disk
      fs.writeFileSync(tempFilePath, file.buffer);

      // Get file info
      const fileInfo = await this.excelStreamProcessor.getExcelFileInfo(tempFilePath);

      return {
        success: true,
        fileInfo: {
          ...fileInfo,
          originalName: file.originalname,
          size: file.size,
          mimeType: file.mimetype,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get file info: ${error.message}`, error.stack);
      return {
        success: false,
        message: `Failed to get file info: ${error.message}`,
      };
    } finally {
      // Clean up temporary file
      if (fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath);
      }
    }
  }

  /**
   * Get B.E.C.E pins statistics
   */
  @Post('bece-pins/stats')
  async getBecePinsStats(): Promise<any> {
    try {
      const availableCount = await this.becePinService.getAvailablePinCount();

      return {
        success: true,
        stats: {
          availablePins: availableCount,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get B.E.C.E pins stats: ${error.message}`, error.stack);
      return {
        success: false,
        message: `Failed to get stats: ${error.message}`,
      };
    }
  }

  /**
   * Custom row processing logic
   */
  private async processRow(row: any, rowIndex: number): Promise<any> {
    // Default processing - just return the row with some metadata
    return {
      originalRow: row,
      processedAt: new Date(),
      rowIndex,
    };
  }

  /**
   * Progress logging
   */
  private logProgress(processed: number): void {
    if (processed % 1000 === 0) {
      this.logger.log(`Progress: ${processed} rows processed`);
    }
  }
}
