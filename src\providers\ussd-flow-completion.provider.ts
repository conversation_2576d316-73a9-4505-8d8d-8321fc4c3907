import { Inject, Injectable } from '@nestjs/common';
import { logger } from '../utils/logging';
import {
  fromInboundUssdRequestToResponsePayload,
  InboundRequestDto,
} from '../ussd/dto/inbound-request.dto';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { RedisCache } from '@tirke/node-cache-manager-ioredis';
import { activeSessionRef } from '../ussd/dto/ussd-stages.dto';
import { USSD_DATA, USSD_SESSION } from '../constants/constants';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { UssdDataDto } from '../ussd/dto/ussd-data.dto';
import {
  base64Encode,
  generateRandomNumber,
  parseJson,
} from '../utils/helpers';
import { catchError, map, mergeMap, of, tap } from 'rxjs';
import {
  IAccountHolderDetails,
  IAPIResponse,
  IPaymentDetails,
  IPaymentSubscriptionErrorResponse,
  OfferRecipientEnums,
} from '../types';
import { AxiosError } from 'axios';
import { OffersDocument } from '../schemas/offers.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Logs, LogsDocument } from '../schemas/logs.schema';
import { Model } from 'mongoose';
import { Purchases, PurchasesDocument } from '../schemas/purchases.schema';
import {
  WaecTransaction,
  WaecTransactionDocument,
} from '../schemas/waec-transaction.schema';

@Injectable()
export class UssdFlowCompletionProvider {
  private readonly basicAuth: string;
  private readonly hashMap: Map<string, string>;
  constructor(
    @Inject(CACHE_MANAGER) private readonly cacheRepository: RedisCache,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    @InjectModel(Logs.name)
    private readonly logsModel: Model<LogsDocument>,
    @InjectModel(Purchases.name)
    private readonly purchasesModel: Model<PurchasesDocument>,
    @InjectModel(WaecTransaction.name)
    private readonly waecTransactionModel: Model<WaecTransactionDocument>,
  ) {
    this.basicAuth = base64Encode(
      `${configService.get('PAYMENT_USERNAME')}:${configService.get('PAYMENT_PASSWORD')}`,
    );
    this.hashMap = new Map();
  }

  public incomingUssdFlowCompletion = async (
    inboundRequest: InboundRequestDto,
  ) => {
    logger.info('Ussd Flow Completion');
    const cachedUssdData = await this.cacheRepository.get<
      UssdDataDto<OffersDocument>
    >(activeSessionRef(inboundRequest.msisdn, USSD_DATA));
    logger.info(
      '%s ::: Cached Ussd Accepted User Inputs ::: %s',
      UssdFlowCompletionProvider.name,
      JSON.stringify(cachedUssdData),
    );
    switch (inboundRequest.ussdString) {
      case '1': {
        const referenceNo = `${Date.now()}${generateRandomNumber()}`;
        this.httpService
          .get<IAPIResponse<IAccountHolderDetails>>(
            `${this.configService.get('PAYMENT_BASE_URL')}/v1/accountholders/${cachedUssdData.msisdn}`,
            {
              timeout: 40000,
              headers: {
                'Content-Type': 'application/json',
                Authorization: `Basic ${this.basicAuth}`,
              },
            },
          )
          .pipe(
            tap(({ status, statusText, data }) =>
              logger.info(
                '%s ::: Response from Accountholder API ::: %s',
                UssdFlowCompletionProvider.name,
                JSON.stringify({ statusText, status, data }),
              ),
            ),
            mergeMap(async ({ data, status, statusText, config }) => {
              logger.info(
                '%s ::: Name enquiry response ::: %s ::: Status ::: %s ::: StatusText ::: %s',
                UssdFlowCompletionProvider.name,
                JSON.stringify(data),
                status,
                statusText,
              );
              logger.info(
                '%s ::: Response payload ::: %s',
                UssdFlowCompletionProvider.name,
                JSON.stringify(data),
              );
              const body = {
                amount: cachedUssdData.offer.price,
                referenceNo,
                payer: data?.data?.accountName,
                payee: 'Kairos',
                narration: `Data - ${cachedUssdData.offer?.volume}${cachedUssdData?.offer?.unitOfMeasure} @ GHC${cachedUssdData.offer.price}`,
                msisdn: cachedUssdData?.msisdn,
                callbackId: this.configService.get<string>(
                  'PAYMENT_CALLBACK_ID',
                ),
              };
              await this.logsModel.create({
                request: body,
                url: config.url,
                headers: config.headers,
                response: null,
                msisdn: body.msisdn,
                event: 'PAYMENT',
                reference: body.referenceNo,
              });
              return body;
            }),
            tap((val) =>
              logger.info(
                '%s ::: Payment Request body ::: %s',
                UssdFlowCompletionProvider.name,
                JSON.stringify(val),
              ),
            ),
            mergeMap((val) => {
              return this.httpService
                .post<IAPIResponse<IPaymentDetails>>(
                  `${this.configService.get('PAYMENT_BASE_URL')}/v1/payment`,
                  val,
                  {
                    timeout: 40000,
                    headers: {
                      'Content-Type': 'application/json',
                      Authorization: `Basic ${this.basicAuth}`,
                    },
                  },
                )
                .pipe(
                  tap(({ data, status, statusText, headers }) =>
                    logger.info(
                      '%s ::: Response coming from the payment request :::: %s',
                      UssdFlowCompletionProvider.name,
                      JSON.stringify({
                        data,
                        status,
                        statusText,
                        headers,
                      }),
                    ),
                  ),
                  map(({ data }) => {
                    logger.info(
                      '%s ::: Response from payment API ::: %s',
                      UssdFlowCompletionProvider.name,
                      JSON.stringify(data),
                    );
                    return { data, body: val };
                  }),
                );
            }),
            catchError((err: AxiosError) => {
              return of({ err, data: null });
            }),
          )
          .subscribe(async (data: any) => {
            if ('err' in data) {
              const errorResponse = data as IPaymentSubscriptionErrorResponse;
              logger.info(
                '%s ::: An error occurred while trigger payment ::: %s :::Request Data ::: %s , Response Data ::: %s',
                UssdFlowCompletionProvider.name,
                errorResponse.err?.code,
                errorResponse?.err?.config?.data,
                errorResponse?.err?.response?.data,
              );
              const updatedLogs = await this.logsModel.findOneAndUpdate(
                {
                  reference: referenceNo,
                },
                {
                  $set: {
                    code: errorResponse?.err?.code,
                    url: errorResponse?.err?.config?.url,
                    request: parseJson(errorResponse?.err?.config.data),
                    event: 'PAYMENT',
                    response:
                      errorResponse.err.response.data ??
                      errorResponse?.err?.stack,
                    headers: errorResponse?.err?.config.headers,
                    reference: referenceNo,
                    msisdn: cachedUssdData?.msisdn,
                  },
                },
                { upsert: true, new: true },
              );
              logger.error(updatedLogs);
            } else {
              const [purchase, log] = await Promise.all([
                this.purchasesModel.create({
                  offer: cachedUssdData.offer,
                  senderMsisdn: cachedUssdData?.msisdn,
                  recipientMsisdn:
                    cachedUssdData.recipientType === OfferRecipientEnums.OTHERS
                      ? cachedUssdData?.recipientPhoneNumber
                      : cachedUssdData.msisdn,
                  paymentReference: referenceNo,
                  totalAmount: cachedUssdData?.offer?.price,
                  overallTotalAmount: data.data?.data?.totalAmount,
                }),
                this.logsModel.updateOne(
                  {
                    reference: referenceNo,
                  },
                  {
                    $set: {
                      response: data.data,
                    },
                  },
                ),
              ]);

              // Also update WAEC transaction if it exists
              try {
                await this.waecTransactionModel.updateOne(
                  { transactionId: cachedUssdData.transactionId },
                  { paymentReference: referenceNo },
                );
              } catch (error) {
                logger.error(
                  '%s ::: Error updating WAEC transaction payment reference: %s',
                  UssdFlowCompletionProvider.name,
                  error.message,
                );
              }

              logger.info(
                '%s ::: Payment initiation completed successfully ::: Purchase Data ->  %s, Log Data -> %s',
                UssdFlowCompletionProvider.name,
                JSON.stringify(purchase),
                JSON.stringify(log),
              );
            }
          });
        await this.cacheRepository.del(
          activeSessionRef(inboundRequest.msisdn, USSD_SESSION),
        );
        await this.cacheRepository.del(
          activeSessionRef(inboundRequest.msisdn, USSD_DATA),
        );
        return fromInboundUssdRequestToResponsePayload(
          inboundRequest,
          "Payment processing.You'll receive prompt soon.",
          '1',
        );
      }
      case '2': {
        logger.info(
          '%s ::: Clearing cache ::::',
          UssdFlowCompletionProvider.name,
        );
        await this.cacheRepository.del(
          activeSessionRef(inboundRequest.msisdn, USSD_SESSION),
        );
        await this.cacheRepository.del(
          activeSessionRef(inboundRequest.msisdn, USSD_DATA),
        );
        return fromInboundUssdRequestToResponsePayload(
          inboundRequest,
          "Operation's been cancelled",
          '1',
        );
      }
    }
  };
}
