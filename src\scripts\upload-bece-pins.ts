/**
 * Utility script to bulk upload B.E.C.E pins and serial numbers to the database
 * Usage: npm run upload-bece-pins
 */

import { MongooseModule } from '@nestjs/mongoose';
import { NestFactory } from '@nestjs/core';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { WaecPin, WaecPinSchema } from '../schemas/waec-pin.schema';
import { BecePinManagementService } from '../services/bece-pin-management.service';
import {
  WaecTransaction,
  WaecTransactionSchema,
} from '../schemas/waec-transaction.schema';
import { Injectable } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import csv from 'csv-parser';
import * as XLSX from 'xlsx';
import { Transform, pipeline } from 'stream';
import { promisify } from 'util';
import { EventEmitter } from 'events';

interface PinData {
  pin: string;
  serialNumber: string;
}

@Injectable()
class BecePinUploadService {
  constructor(
    private readonly becePinManagementService: BecePinManagementService,
  ) { }

  /**
   * Upload pins from a CSV file
   * CSV format: pin,serialNumber
   * @param filePath Path to the CSV file
   * @param price Price per pin
   */
  async uploadFromCsv(filePath: string, price: number): Promise<void> {
    const pins: PinData[] = [];

    return new Promise((resolve, reject) => {
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (row) => {
          if (row.pin && row.serialNumber) {
            pins.push({
              pin: row.pin.trim(),
              serialNumber: row.serialNumber.trim(),
            });
          }
        })
        .on('end', async () => {
          try {
            console.log(`Parsed ${pins.length} pins from CSV file`);
            const uploadedCount = await this.becePinManagementService.bulkUploadPins(
              pins,
              price,
            );
            console.log(`Successfully uploaded ${uploadedCount} B.E.C.E pins`);
            resolve();
          } catch (error) {
            console.error('Error uploading pins:', error.message);
            reject(error);
          }
        })
        .on('error', (error) => {
          console.error('Error reading CSV file:', error.message);
          reject(error);
        });
    });
  }

  /**
   * Upload pins from a JSON file
   * JSON format: [{"pin": "123456", "serialNumber": "SN001"}, ...]
   * @param filePath Path to the JSON file
   * @param price Price per pin
   */
  async uploadFromJson(filePath: string, price: number): Promise<void> {
    try {
      const fileContent = fs.readFileSync(filePath, 'utf8');
      const pins: PinData[] = JSON.parse(fileContent);

      if (!Array.isArray(pins)) {
        throw new Error('JSON file must contain an array of pin objects');
      }

      // Validate pin data
      const validPins = pins.filter((pin) => pin.pin && pin.serialNumber);

      if (validPins.length !== pins.length) {
        console.warn(`${pins.length - validPins.length} invalid pins were skipped`);
      }

      console.log(`Uploading ${validPins.length} pins from JSON file`);
      const uploadedCount = await this.becePinManagementService.bulkUploadPins(
        validPins,
        price,
      );
      console.log(`Successfully uploaded ${uploadedCount} B.E.C.E pins`);
    } catch (error) {
      console.error('Error uploading pins from JSON:', error.message);
      throw error;
    }
  }

  /**
   * Generate sample pins for testing
   * @param count Number of pins to generate
   * @param price Price per pin
   */
  async generateSamplePins(count: number, price: number): Promise<void> {
    const pins: PinData[] = [];

    for (let i = 1; i <= count; i++) {
      pins.push({
        pin: this.generateRandomPin(),
        serialNumber: `BECE${String(i).padStart(6, '0')}`,
      });
    }

    console.log(`Generating ${pins.length} sample B.E.C.E pins`);
    const uploadedCount = await this.becePinManagementService.bulkUploadPins(
      pins,
      price,
    );
    console.log(`Successfully uploaded ${uploadedCount} sample B.E.C.E pins`);
  }

  /**
   * Generate a random 10-digit PIN
   */
  private generateRandomPin(): string {
    return Math.floor(1000000000 + Math.random() * 9000000000).toString();
  }

  /**
   * Get statistics about uploaded pins
   */
  async getStatistics(): Promise<void> {
    const availableCount = await this.becePinManagementService.getAvailablePinCount();
    console.log(`Available B.E.C.E pins: ${availableCount}`);
  }
}

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>('MONGODB_URI'),
      }),
      inject: [ConfigService],
    }),
    MongooseModule.forFeature([
      { name: WaecPin.name, schema: WaecPinSchema },
      { name: WaecTransaction.name, schema: WaecTransactionSchema },
    ]),
  ],
  providers: [BecePinUploadService, BecePinManagementService],
})
class BecePinUploadModule { }

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(BecePinUploadModule);
  const uploadService = app.get(BecePinUploadService);

  try {
    // Get command line arguments
    const args = process.argv.slice(2);
    const command = args[0];

    switch (command) {
      case 'csv':
        if (args.length < 3) {
          console.error('Usage: npm run upload-bece-pins csv <file-path> <price>');
          process.exit(1);
        }
        await uploadService.uploadFromCsv(args[1], parseFloat(args[2]));
        break;

      case 'json':
        if (args.length < 3) {
          console.error('Usage: npm run upload-bece-pins json <file-path> <price>');
          process.exit(1);
        }
        await uploadService.uploadFromJson(args[1], parseFloat(args[2]));
        break;

      case 'generate':
        if (args.length < 3) {
          console.error('Usage: npm run upload-bece-pins generate <count> <price>');
          process.exit(1);
        }
        await uploadService.generateSamplePins(parseInt(args[1]), parseFloat(args[2]));
        break;

      case 'stats':
        await uploadService.getStatistics();
        break;

      default:
        console.log('Available commands:');
        console.log('  csv <file-path> <price>     - Upload pins from CSV file');
        console.log('  json <file-path> <price>    - Upload pins from JSON file');
        console.log('  generate <count> <price>    - Generate sample pins');
        console.log('  stats                       - Show pin statistics');
        console.log('');
        console.log('Examples:');
        console.log('  npm run upload-bece-pins csv ./pins.csv 12.0');
        console.log('  npm run upload-bece-pins json ./pins.json 12.0');
        console.log('  npm run upload-bece-pins generate 100 12.0');
        console.log('  npm run upload-bece-pins stats');
        break;
    }
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  } finally {
    await app.close();
  }
}

bootstrap();
