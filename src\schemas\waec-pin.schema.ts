import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type WaecPinDocument = WaecPin & Document;

@Schema({ timestamps: true })
export class WaecPin {
  @Prop({ required: true })
  pin: string; // Encrypted PIN

  @Prop({ required: true, unique: true })
  serialNumber: string;

  @Prop({ required: false, enum: ['WASSCE', 'BECE', 'NOVDEC', 'ABCE/GBCE'] })
  examType: string;

  @Prop({
    required: false,
    enum: ['AVAILABLE', 'PURCHASED', 'USED'],
    default: 'AVAILABLE',
    index: true
  })
  status: string;

  @Prop({ required: false })
  price: number;

  @Prop({ required: false })
  purchasedBy: string; // MSISDN of purchaser

  @Prop({ required: false })
  msisdn: string; // Alternative field for MSISDN

  @Prop({ default: null })
  purchasedAt: Date;

  @Prop()
  email: string;

  @Prop({ required: false })
  transactionId: string;

  @Prop({ default: false })
  used: boolean;

  @Prop({ default: false })
  isDelivered: boolean; // Whether PIN has been delivered via SMS/Email

  @Prop({ default: null })
  deliveredAt: Date;

  @Prop({ default: 'SMS', enum: ['SMS', 'EMAIL'] })
  deliveryMethod: string;

  @Prop()
  network: string;
}

export const WaecPinSchema = SchemaFactory.createForClass(WaecPin);

// Create indexes for efficient queries (from BecePin schema)
WaecPinSchema.index({ status: 1 });
WaecPinSchema.index({ serialNumber: 1 });
WaecPinSchema.index({ purchasedBy: 1 });
WaecPinSchema.index({ transactionId: 1 });
WaecPinSchema.index({ status: 1, createdAt: 1 }); // For getting available pins in order
