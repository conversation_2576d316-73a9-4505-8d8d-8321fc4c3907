import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type WaecPinDocument = WaecPin & Document;

@Schema({ timestamps: true })
export class WaecPin {
  @Prop({ required: true })
  pin: string;

  @Prop({ required: true })
  serialNumber: string;

  @Prop({ required: false, enum: ['WASSCE', 'BECE', 'NOVDEC', 'ABCE/GBCE'] })
  examType: string;

  @Prop({ required: false })
  price: number;

  @Prop({ required: false })
  purchasedBy: string;

  @Prop({ required: false })
  msisdn: string;

  @Prop()
  email: string;

  @Prop({ required: false })
  transactionId: string;

  @Prop({ default: false })
  used: boolean;

  @Prop({ default: false })
  isDelivered: boolean;

  @Prop()
  network: string;
}

export const WaecPinSchema = SchemaFactory.createForClass(WaecPin);
