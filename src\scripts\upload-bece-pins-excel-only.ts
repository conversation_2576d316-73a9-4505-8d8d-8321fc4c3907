/**
 * Simplified B.E.C.E pins upload utility - Excel files only
 * This version removes CSV dependency and focuses only on Excel files
 */

import { MongooseModule } from '@nestjs/mongoose';
import { NestFactory } from '@nestjs/core';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { WaecPin, WaecPinSchema } from '../schemas/waec-pin.schema';
import { BecePinManagementService } from '../services/bece-pin-management.service';
import {
  WaecTransaction,
  WaecTransactionSchema,
} from '../schemas/waec-transaction.schema';
import { Injectable } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import * as XLSX from 'xlsx';

interface PinData {
  pin: string;
  serialNumber: string;
}

@Injectable()
class BecePinUploadService {
  constructor(
    private readonly becePinManagementService: BecePinManagementService,
  ) { }

  /**
   * Upload pins from an Excel file (.xlsx, .xls)
   * Excel format: Column A = PIN, Column B = Serial Number
   * @param filePath Path to the Excel file
   * @param price Price per pin
   */
  async uploadFromExcel(filePath: string, price: number): Promise<void> {
    try {
      console.log(`Reading Excel file: ${filePath}`);

      // Read the Excel file
      const workbook = XLSX.readFile(filePath);
      const sheetName = workbook.SheetNames[0]; // Use first sheet
      const worksheet = workbook.Sheets[sheetName];

      // Convert to JSON
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      const pins: PinData[] = [];
      let skippedRows = 0;

      // Process each row (skip header row)
      for (let i = 1; i < jsonData.length; i++) {
        const row = jsonData[i] as any[];

        if (row && row.length >= 2) {
          const pin = (row[0] || '').toString().trim();
          const serialNumber = (row[1] || '').toString().trim();

          if (pin && serialNumber) {
            pins.push({ pin, serialNumber });
          } else {
            skippedRows++;
          }
        } else {
          skippedRows++;
        }
      }

      if (skippedRows > 0) {
        console.warn(`Skipped ${skippedRows} invalid rows`);
      }

      console.log(`Uploading ${pins.length} pins from Excel file`);
      const uploadedCount = await this.becePinManagementService.bulkUploadPins(
        pins,
        price,
      );
      console.log(`Successfully uploaded ${uploadedCount} B.E.C.E pins`);
    } catch (error) {
      console.error('Error uploading pins from Excel:', error.message);
      throw error;
    }
  }

  /**
   * Upload pins from a JSON file
   * JSON format: [{"pin": "123456", "serialNumber": "SN001"}, ...]
   * @param filePath Path to the JSON file
   * @param price Price per pin
   */
  async uploadFromJson(filePath: string, price: number): Promise<void> {
    try {
      const fileContent = fs.readFileSync(filePath, 'utf8');
      const pins: PinData[] = JSON.parse(fileContent);

      if (!Array.isArray(pins)) {
        throw new Error('JSON file must contain an array of pin objects');
      }

      // Validate pin data
      const validPins = pins.filter((pin) => pin.pin && pin.serialNumber);

      if (validPins.length !== pins.length) {
        console.warn(`${pins.length - validPins.length} invalid pins were skipped`);
      }

      console.log(`Uploading ${validPins.length} pins from JSON file`);
      const uploadedCount = await this.becePinManagementService.bulkUploadPins(
        validPins,
        price,
      );
      console.log(`Successfully uploaded ${uploadedCount} B.E.C.E pins`);
    } catch (error) {
      console.error('Error uploading pins from JSON:', error.message);
      throw error;
    }
  }

  /**
   * Generate sample pins for testing
   * @param count Number of pins to generate
   * @param price Price per pin
   */
  async generateSamplePins(count: number, price: number): Promise<void> {
    const pins: PinData[] = [];

    for (let i = 1; i <= count; i++) {
      pins.push({
        pin: this.generateRandomPin(),
        serialNumber: `BECE${String(i).padStart(6, '0')}`,
      });
    }

    console.log(`Generating ${pins.length} sample B.E.C.E pins`);
    const uploadedCount = await this.becePinManagementService.bulkUploadPins(
      pins,
      price,
    );
    console.log(`Successfully uploaded ${uploadedCount} sample B.E.C.E pins`);
  }

  /**
   * Generate a random 10-digit PIN
   */
  private generateRandomPin(): string {
    return Math.floor(1000000000 + Math.random() * 9000000000).toString();
  }

  /**
   * Get statistics about uploaded pins
   */
  async getStatistics(): Promise<void> {
    const availableCount = await this.becePinManagementService.getAvailablePinCount();
    console.log(`Available B.E.C.E pins: ${availableCount}`);
  }

  /**
   * Create a sample Excel file for testing
   */
  createSampleExcelFile(fileName: string, pinCount: number): string {
    console.log(`Creating sample Excel file with ${pinCount} pins...`);

    const workbook = XLSX.utils.book_new();
    const data = [];

    // Add header row
    data.push(['PIN', 'Serial Number']);

    // Generate test pins
    for (let i = 1; i <= pinCount; i++) {
      const pin = this.generateRandomPin();
      const serialNumber = `BECE${String(i).padStart(6, '0')}`;
      data.push([pin, serialNumber]);
    }

    const worksheet = XLSX.utils.aoa_to_sheet(data);
    XLSX.utils.book_append_sheet(workbook, worksheet, 'BECEPins');

    const filePath = path.join(process.cwd(), fileName);
    XLSX.writeFile(workbook, filePath);

    console.log(`Sample Excel file created: ${filePath}`);
    return filePath;
  }
}

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>('MONGODB_URI'),
      }),
      inject: [ConfigService],
    }),
    MongooseModule.forFeature([
      { name: WaecPin.name, schema: WaecPinSchema },
      { name: WaecTransaction.name, schema: WaecTransactionSchema },
    ]),
  ],
  providers: [BecePinUploadService, BecePinManagementService],
})
class BecePinUploadModule { }

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(BecePinUploadModule);
  const uploadService = app.get(BecePinUploadService);

  try {
    // Get command line arguments
    const args = process.argv.slice(2);
    const command = args[0];

    switch (command) {
      case 'excel':
        if (args.length < 3) {
          console.error('Usage: yarn upload-bece-pins-excel excel <file-path> <price>');
          process.exit(1);
        }
        await uploadService.uploadFromExcel(args[1], parseFloat(args[2]));
        break;

      case 'json':
        if (args.length < 3) {
          console.error('Usage: yarn upload-bece-pins-excel json <file-path> <price>');
          process.exit(1);
        }
        await uploadService.uploadFromJson(args[1], parseFloat(args[2]));
        break;

      case 'generate':
        if (args.length < 3) {
          console.error('Usage: yarn upload-bece-pins-excel generate <count> <price>');
          process.exit(1);
        }
        await uploadService.generateSamplePins(parseInt(args[1]), parseFloat(args[2]));
        break;

      case 'create-sample':
        if (args.length < 2) {
          console.error('Usage: yarn upload-bece-pins-excel create-sample <count> [filename]');
          process.exit(1);
        }
        const fileName = args[2] || 'sample-bece-pins.xlsx';
        uploadService.createSampleExcelFile(fileName, parseInt(args[1]));
        break;

      case 'stats':
        await uploadService.getStatistics();
        break;

      default:
        console.log('Available commands (Excel-only version):');
        console.log('  excel <file-path> <price>       - Upload pins from Excel file');
        console.log('  json <file-path> <price>        - Upload pins from JSON file');
        console.log('  generate <count> <price>        - Generate sample pins');
        console.log('  create-sample <count> [file]    - Create sample Excel file');
        console.log('  stats                           - Show pin statistics');
        console.log('');
        console.log('Examples:');
        console.log('  yarn upload-bece-pins-excel excel ./pins.xlsx 12.0');
        console.log('  yarn upload-bece-pins-excel create-sample 100');
        console.log('  yarn upload-bece-pins-excel generate 100 12.0');
        console.log('  yarn upload-bece-pins-excel stats');
        break;
    }
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  } finally {
    await app.close();
  }
}

bootstrap();
