import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, SchemaTypes } from 'mongoose';

export type TransactionDocument = HydratedDocument<Transaction>;
export type TransactionStatuses =
  | 'PENDING'
  | 'COMPLETED'
  | 'SUCCESS'
  | 'FAILED';

@Schema({
  collection: 'transactions',
  timestamps: true,
})
export class Transaction {
  @Prop({
    type: String,
    enum: ['PENDING', 'COMPLETED', 'SUCCESS', 'FAILED'],
    index: true,
    default: 'PENDING',
  })
  status: string;

  @Prop({
    type: String,
    default: null,
    index: true,
  })
  msisdn: string;

  @Prop({
    index: true,
  })
  reference: string;

  @Prop()
  gtBankTransactionId: string;

  @Prop()
  providerTransactionId: string;

  @Prop({
    type: String,
    default: null,
  })
  paymentRequestStatus: string;

  @Prop({
    type: Number,
    default: 0,
  })
  amount: number;

  @Prop({
    type: String,
    enum: ['NOT_PUSHED', 'PUSHED'],
    default: 'NOT_PUSHED',
  })
  callbackPushStatus: string;

  @Prop({
    type: String,
  })
  message: string;

  @Prop({
    type: Number,
    default: null,
  })
  totalAmount: number;

  @Prop({
    type: String,
    default: null,
  })
  network: string;

  @Prop({
    type: Number,
  })
  charge: number;

  @Prop({
    type: SchemaTypes.ObjectId,
    ref: 'Credential',
    default: null,
  })
  credentialId: string;

  @Prop({
    type: SchemaTypes.ObjectId,
    ref: 'Invoice',
    default: null,
  })
  invoiceId: string;

  @Prop({
    type: String,
    default: null,
  })
  ipAddress: string;
}

export const TransactionSchema = SchemaFactory.createForClass(Transaction);
TransactionSchema.index({ reference: 1 });
TransactionSchema.index({ status: 1 });
TransactionSchema.index({ credentialId: 1, status: 1 });
TransactionSchema.index({ credentialId: 1, createdAt: 1, status: 1 });