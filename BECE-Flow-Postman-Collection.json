{"info": {"name": "BECE Pin Purchase Flow", "description": "Complete testing collection for BECE pin purchase via USSD *889*3#", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000/api/v1"}, {"key": "sessionId", "value": "test-session-{{$timestamp}}"}, {"key": "msisdn", "value": "233501234567"}], "item": [{"name": "1. Check Available Pins", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/excel/bece-pins/stats", "host": ["{{baseUrl}}"], "path": ["excel", "bece-pins", "stats"]}}}, {"name": "2. USSD - Start Session", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sessionId\": \"{{sessionId}}\",\n  \"msisdn\": \"{{msisdn}}\",\n  \"ussdString\": \"\",\n  \"network\": \"MTN\"\n}"}, "url": {"raw": "{{baseUrl}}/ussd/waec", "host": ["{{baseUrl}}"], "path": ["ussd", "waec"]}}}, {"name": "3. USSD - Select BECE", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sessionId\": \"{{sessionId}}\",\n  \"msisdn\": \"{{msisdn}}\",\n  \"ussdString\": \"1\",\n  \"network\": \"MTN\"\n}"}, "url": {"raw": "{{baseUrl}}/ussd/waec", "host": ["{{baseUrl}}"], "path": ["ussd", "waec"]}}}, {"name": "4. USSD - Select SMS Delivery", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sessionId\": \"{{sessionId}}\",\n  \"msisdn\": \"{{msisdn}}\",\n  \"ussdString\": \"2\",\n  \"network\": \"MTN\"\n}"}, "url": {"raw": "{{baseUrl}}/ussd/waec", "host": ["{{baseUrl}}"], "path": ["ussd", "waec"]}}}, {"name": "5. USSD - Enter Quantity", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sessionId\": \"{{sessionId}}\",\n  \"msisdn\": \"{{msisdn}}\",\n  \"ussdString\": \"2\",\n  \"network\": \"MTN\"\n}"}, "url": {"raw": "{{baseUrl}}/ussd/waec", "host": ["{{baseUrl}}"], "path": ["ussd", "waec"]}}}, {"name": "6. USSD - Confirm Payment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sessionId\": \"{{sessionId}}\",\n  \"msisdn\": \"{{msisdn}}\",\n  \"ussdString\": \"1\",\n  \"network\": \"MTN\"\n}"}, "url": {"raw": "{{baseUrl}}/ussd/waec", "host": ["{{baseUrl}}"], "path": ["ussd", "waec"]}}}, {"name": "7. Webhook - Payment Completion", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reference\": \"REPLACE_WITH_PAYMENT_REF\",\n  \"status\": \"COMPLETED\",\n  \"amount\": 2400,\n  \"currency\": \"GHS\",\n  \"customer\": {\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"{{msisdn}}\"\n  },\n  \"metadata\": {\n    \"examType\": \"BECE\",\n    \"quantity\": 2\n  }\n}"}, "url": {"raw": "{{baseUrl}}/webhook", "host": ["{{baseUrl}}"], "path": ["webhook"]}}}]}