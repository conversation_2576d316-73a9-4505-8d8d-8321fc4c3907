import { Test, TestingModule } from '@nestjs/testing';
import { UssdService } from './ussd.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { ConfigService } from '@nestjs/config';
import { RedisCache } from '@tirke/node-cache-manager-ioredis';
import {
  CachedUssdMenuMenuStepsMockData,
  InboundUssdRequestResponseMockData,
  MOCK_SESSION_ID,
} from './__mocks__/ussd.mock';
import * as Helpers from '../utils/helpers';
import { activeSessionRef } from './dto/ussd-stages.dto';
import {
  ACTIVE_TTL,
  MENU_OPTIONS,
  MENU_TITLE,
  USSD_SESSION,
} from '../constants/constants';
import { UssdStageInputProvider } from '../providers/ussd-stage-input.provider';

describe('UssdService', () => {
  let service: UssdService;
  let configService: ConfigService;
  let cacheRepository: RedisCache;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UssdService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
            set: jest.fn(),
          },
        },
        {
          provide: CACHE_MANAGER,
          useValue: {
            set: jest.fn(),
            get: jest.fn(),
          },
        },
        {
          provide: UssdStageInputProvider,
          useValue: {},
        },
      ],
    }).compile();
    configService = module.get(ConfigService);
    cacheRepository = module.get(CACHE_MANAGER);
    service = module.get<UssdService>(UssdService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('#ConfigService', () => {
    it('should set and get values set as env. variable', () => {
      jest.spyOn(configService, 'set');
      jest.spyOn(configService, 'get').mockReturnValue(MOCK_SESSION_ID);
      configService.set('SESSION_ID', MOCK_SESSION_ID);

      expect(configService.set).toHaveBeenCalledWith(
        'SESSION_ID',
        MOCK_SESSION_ID,
      );
      expect(configService.get('SESSION_ID')).toEqual(MOCK_SESSION_ID);
    });
  });

  describe('#RedisCache', () => {
    it('should return a cached step for the ussd flow', async () => {
      jest.spyOn(cacheRepository, 'set');
      jest
        .spyOn(cacheRepository, 'get')
        .mockResolvedValue(CachedUssdMenuMenuStepsMockData);
      expect(cacheRepository).toHaveProperty('get');
      expect(cacheRepository).toHaveProperty('set');
      await cacheRepository.set(
        MOCK_SESSION_ID,
        CachedUssdMenuMenuStepsMockData,
      );
      const cached = await cacheRepository.get(MOCK_SESSION_ID);
      expect(cacheRepository.set).toHaveBeenCalled();
      expect(cached).toBe(CachedUssdMenuMenuStepsMockData);
      expect(cached).toHaveLength(1);
    });
  });

  describe('#ServiceCodeValidation', () => {
    it('should validate service code ', () => {
      const serviceCode = Helpers.validateServiceCode(
        InboundUssdRequestResponseMockData,
        '',
      );
      expect(serviceCode).toBeTruthy();
    });
  });

  describe('#MenuUSSDMenuItems', () => {
    it('should return the main menu items of the ussd application', async () => {
      jest.spyOn(Helpers, 'validateServiceCode').mockReturnValue(true);
      jest
        .spyOn(service, 'handleContinueSession')
        .mockResolvedValue('Test TEst');
      jest.spyOn(cacheRepository, 'set');
      configService.set('SERVICE_CODE', '*899#');

      await cacheRepository.set(
        activeSessionRef(MOCK_SESSION_ID, USSD_SESSION),
        CachedUssdMenuMenuStepsMockData,
        ACTIVE_TTL,
      );
      const response = await service.processInboundRequest(
        InboundUssdRequestResponseMockData,
      );
      expect(response.ussdString).toEqual(`${MENU_TITLE}${MENU_OPTIONS}`);
      expect(response).toHaveProperty('sessionId', MOCK_SESSION_ID);
    });
  });
});
