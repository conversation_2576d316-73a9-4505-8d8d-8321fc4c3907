import { HttpAdapterHost, NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';
import { NestExpressApplication } from '@nestjs/platform-express';
import helmet from 'helmet';
import { json, urlencoded } from 'express';
import cookieParser from 'cookie-parser';
import compression from 'compression';
import * as requestIp from 'request-ip';
import { logger } from './utils/logging';
import { apiPrefix } from './utils/helpers';
import { AllExceptionsFilter } from './exceptions/all-exceptions.filter';
import { CACHE_MANAGER } from '@nestjs/cache-manager';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    bodyParser: false,
  });
  const configService = app.get(ConfigService);

  app.enableCors();
  app.use(helmet());
  app.use(cookieParser());
  app.use(
    json({
      limit: '50mb',
    }),
  );
  app.use(
    urlencoded({
      extended: true,
      limit: '50mb',
    }),
  );

  app.use(compression());
  app.set('trust proxy', true);

  app.use(requestIp.mw());

  const httpAdapterHost = app.get(HttpAdapterHost);
  const cacheManager = app.get(CACHE_MANAGER);
  app.useGlobalFilters(new AllExceptionsFilter(httpAdapterHost, cacheManager));
  app.setGlobalPrefix(apiPrefix(configService.get('NODE_ENV')));
  await app.listen(configService.get('PORT') || 3000, () => {
    logger.info(
      `%s ::: datashare-ussd-api up and running on port : %s`,
      `Datashare USSD API Service`,
      configService.get('PORT'),
    );
  });
}
bootstrap();
