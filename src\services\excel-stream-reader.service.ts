import { Injectable, Logger } from '@nestjs/common';
import { Transform, pipeline } from 'stream';
import { promisify } from 'util';
import * as fs from 'fs';
import * as XLSX from 'xlsx';

const pipelineAsync = promisify(pipeline);

export interface StreamingOptions {
  sheetName?: string;
  sheetIndex?: number;
  batchSize?: number;
  skipEmptyRows?: boolean;
  headerRow?: number;
  maxRows?: number;
  onProgress?: (processed: number) => void;
  onError?: (error: Error, rowIndex: number) => void;
}

export interface RowProcessingResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  rowIndex: number;
}

@Injectable()
export class ExcelStreamReaderService {
  private readonly logger = new Logger(ExcelStreamReaderService.name);

  /**
   * Process Excel file using XLSX streaming with Transform streams
   * This follows the pattern you requested with memory-efficient processing
   */
  async processExcelWithTransform<T>(
    filePath: string,
    processor: (row: any, rowIndex: number) => Promise<T> | T,
    options: StreamingOptions = {},
  ): Promise<{
    totalProcessed: number;
    successCount: number;
    errorCount: number;
    errors: Array<{ rowIndex: number; error: string }>;
  }> {
    const {
      sheetName,
      sheetIndex = 0,
      batchSize = 1000,
      skipEmptyRows = true,
      headerRow = 0,
      maxRows,
      onProgress,
      onError,
    } = options;

    this.logger.log(`Starting streaming Excel processing: ${filePath}`);

    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }

    const stats = {
      totalProcessed: 0,
      successCount: 0,
      errorCount: 0,
      errors: [] as Array<{ rowIndex: number; error: string }>,
    };

    try {
      // Read workbook with minimal options for memory efficiency
      const workbook = XLSX.readFile(filePath, {
        cellDates: true,
        cellNF: false,
        cellText: false,
        sheetStubs: false,
        bookDeps: false,
        bookFiles: false,
        bookProps: false,
        bookSheets: false,
        bookVBA: false,
      });

      const worksheetName = sheetName || workbook.SheetNames[sheetIndex];
      const worksheet = workbook.Sheets[worksheetName];

      if (!worksheet) {
        throw new Error(`Worksheet not found: ${worksheetName}`);
      }

      // Create streaming pipeline following your requested pattern
      const jsonStream = XLSX.stream.to_json(worksheet, {
        raw: true,
        defval: null,
        blankrows: !skipEmptyRows,
      });

      let headerData: any = null;
      let currentRowIndex = 0;
      const self = this; // Capture the service instance

      const processingTransform = new Transform({
        objectMode: true,
        readableObjectMode: true,
        writableObjectMode: true,
        async transform(chunk, _encoding, callback) {
          try {
            // Handle header row
            if (currentRowIndex === headerRow) {
              headerData = chunk;
              currentRowIndex++;
              callback();
              return;
            }

            // Skip empty rows if requested
            if (skipEmptyRows && self.isEmptyRow(chunk)) {
              currentRowIndex++;
              callback();
              return;
            }

            // Apply max rows limit
            if (maxRows && currentRowIndex >= maxRows) {
              callback();
              return;
            }

            // Convert row using headers if available
            let processedRow = chunk;
            if (headerData) {
              processedRow = self.mapRowToHeaders(chunk, headerData);
            }

            // Process the row
            try {
              const result = await processor(processedRow, currentRowIndex);
              stats.successCount++;
              stats.totalProcessed++;

              // Report progress
              if (onProgress && stats.totalProcessed % batchSize === 0) {
                onProgress(stats.totalProcessed);
              }

              // Pass result downstream
              this.push({
                success: true,
                data: result,
                rowIndex: currentRowIndex,
              });
            } catch (processingError) {
              stats.errorCount++;
              stats.totalProcessed++;
              const errorMsg = processingError.message || 'Unknown processing error';
              stats.errors.push({ rowIndex: currentRowIndex, error: errorMsg });

              if (onError) {
                onError(processingError, currentRowIndex);
              }

              this.push({
                success: false,
                error: errorMsg,
                rowIndex: currentRowIndex,
              });
            }

            currentRowIndex++;
            callback();
          } catch (error) {
            callback(error);
          }
        },
      });

      // Create a sink transform to consume the processed data
      const sinkTransform = new Transform({
        objectMode: true,
        transform(_chunk: RowProcessingResult<T>, _encoding, callback) {
          // You can add additional processing here if needed
          // For now, just consume the data
          callback();
        },
      });

      // Execute the streaming pipeline
      await pipelineAsync(jsonStream, processingTransform, sinkTransform);

      this.logger.log(
        `Streaming processing completed. Processed: ${stats.totalProcessed}, Success: ${stats.successCount}, Errors: ${stats.errorCount}`,
      );

      return stats;
    } catch (error) {
      this.logger.error(`Streaming Excel processing failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Alternative method using manual row-by-row reading for maximum memory efficiency
   */
  async processExcelRowByRow<T>(
    filePath: string,
    processor: (row: any, rowIndex: number) => Promise<T> | T,
    options: StreamingOptions = {},
  ): Promise<{
    totalProcessed: number;
    successCount: number;
    errorCount: number;
    errors: Array<{ rowIndex: number; error: string }>;
  }> {
    const {
      sheetName,
      sheetIndex = 0,
      skipEmptyRows = true,
      headerRow = 0,
      maxRows,
      onProgress,
      onError,
    } = options;

    this.logger.log(`Starting row-by-row Excel processing: ${filePath}`);

    const stats = {
      totalProcessed: 0,
      successCount: 0,
      errorCount: 0,
      errors: [] as Array<{ rowIndex: number; error: string }>,
    };

    try {
      // Read workbook with minimal memory footprint
      const workbook = XLSX.readFile(filePath, {
        cellDates: true,
        sheetStubs: false,
      });

      const worksheetName = sheetName || workbook.SheetNames[sheetIndex];
      const worksheet = workbook.Sheets[worksheetName];

      if (!worksheet) {
        throw new Error(`Worksheet not found: ${worksheetName}`);
      }

      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1');
      const totalRows = range.e.r - range.s.r + 1;
      const effectiveMaxRows = maxRows ? Math.min(maxRows, totalRows) : totalRows;

      let headerData: any = null;

      // Process rows one by one
      for (let rowIndex = range.s.r; rowIndex <= range.e.r && rowIndex < effectiveMaxRows; rowIndex++) {
        try {
          // Read row data
          const rowData: any = {};
          let hasData = false;

          for (let colIndex = range.s.c; colIndex <= range.e.c; colIndex++) {
            const cellAddress = XLSX.utils.encode_cell({ r: rowIndex, c: colIndex });
            const cell = worksheet[cellAddress];

            if (cell) {
              const columnName = XLSX.utils.encode_col(colIndex);
              rowData[columnName] = this.getCellValue(cell);
              hasData = true;
            }
          }

          // Handle header row
          if (rowIndex === headerRow) {
            headerData = rowData;
            continue;
          }

          // Skip empty rows if requested
          if (skipEmptyRows && !hasData) {
            continue;
          }

          // Convert row using headers if available
          let processedRow = rowData;
          if (headerData) {
            processedRow = this.mapRowToHeaders(rowData, headerData);
          }

          // Process the row
          try {
            await processor(processedRow, rowIndex);
            stats.successCount++;
          } catch (processingError) {
            stats.errorCount++;
            const errorMsg = processingError.message || 'Unknown processing error';
            stats.errors.push({ rowIndex, error: errorMsg });

            if (onError) {
              onError(processingError, rowIndex);
            }
          }

          stats.totalProcessed++;

          // Report progress
          if (onProgress && stats.totalProcessed % 100 === 0) {
            onProgress(stats.totalProcessed);
          }

          // Force garbage collection periodically for large files
          if (stats.totalProcessed % 10000 === 0 && global.gc) {
            global.gc();
          }
        } catch (rowError) {
          this.logger.error(`Error processing row ${rowIndex}: ${rowError.message}`);
          stats.errorCount++;
          stats.errors.push({ rowIndex, error: rowError.message });
        }
      }

      this.logger.log(
        `Row-by-row processing completed. Processed: ${stats.totalProcessed}, Success: ${stats.successCount}, Errors: ${stats.errorCount}`,
      );

      return stats;
    } catch (error) {
      this.logger.error(`Row-by-row Excel processing failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get cell value with proper type conversion
   */
  private getCellValue(cell: XLSX.CellObject): any {
    if (!cell) return null;

    switch (cell.t) {
      case 'n': // Number
        return cell.v;
      case 's': // String
        return cell.v;
      case 'b': // Boolean
        return cell.v;
      case 'd': // Date
        return cell.v;
      case 'e': // Error
        return null;
      default:
        return cell.v;
    }
  }

  /**
   * Check if row is empty
   */
  private isEmptyRow(data: any): boolean {
    if (!data) return true;
    return Object.values(data).every(value =>
      value === null || value === undefined || value === '' || value === 0
    );
  }

  /**
   * Map row data to header keys
   */
  private mapRowToHeaders(data: any, headerData: any): any {
    const mapped: any = {};
    const headerKeys = Object.keys(headerData);
    const dataKeys = Object.keys(data);

    for (let i = 0; i < Math.max(headerKeys.length, dataKeys.length); i++) {
      const headerKey = headerKeys[i];
      const dataKey = dataKeys[i];

      if (headerKey && dataKey && headerData[headerKey]) {
        mapped[headerData[headerKey]] = data[dataKey];
      }
    }

    return mapped;
  }
}
